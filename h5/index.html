<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/logo.jpg" />
    <meta
      name="viewport"
      content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover"
    />
    <meta name="format-detection" content="telephone=no, email=no, date=no, address=no" />
    <title>家园议事厅</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <script>
      window.onload = function () {
        document.addEventListener('touchstart', function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        });

        var lastTouchEnd = 0;

        document.addEventListener(
          'touchend',
          function (event) {
            var now = new Date().getTime();
            if (now - lastTouchEnd <= 300) {
              event.preventDefault();
            }

            lastTouchEnd = now;
          },
          false,
        );

        document.addEventListener('gesturestart', function (event) {
          event.preventDefault();
        });
      };
    </script>
  </body>
</html>
