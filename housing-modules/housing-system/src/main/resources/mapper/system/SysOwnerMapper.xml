<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.housing.system.mapper.SysOwnerMapper">

    <resultMap type="org.housing.system.domain.vo.SysOwnerVo" id="SysOwnerResult">
    </resultMap>

    <select id="selectPageOwnerList" resultMap="SysOwnerResult">
        SELECT
            o.* ,
            h.housing_name
        FROM
            sys_owner o
                JOIN sys_housing h ON o.housing_id = h.housing_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectOwnerList" resultMap="SysOwnerResult">
        SELECT
            o.* ,
            h.housing_name
        FROM
            sys_owner o
                JOIN sys_housing h ON o.housing_id = h.housing_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectOwnerById" resultMap="SysOwnerResult">
        SELECT
            o.* ,
            h.housing_name
        FROM
            sys_owner o
                JOIN sys_housing h ON o.housing_id = h.housing_id
        WHERE
            o.owner_id = #{ownerId}
    </select>
</mapper>
