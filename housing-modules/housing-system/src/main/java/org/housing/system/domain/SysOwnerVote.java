package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 业主投票对象 sys_owner_vote
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_owner_vote")
public class SysOwnerVote implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业主投票主键
     */
    @TableId(value = "owner_vote_id")
    private Long ownerVoteId;

    /**
     * 业主主键
     */
    private Long ownerId;

    /**
     * 投票主键
     */
    private Long voteId;

    /**
     * 意见
     */
    private String advice;

    /**
     * 来源，0-线下，1-小程序，2-电话
     */
    private String resource;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
