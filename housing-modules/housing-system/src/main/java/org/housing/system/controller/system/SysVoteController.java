package org.housing.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.R;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.idempotent.annotation.RepeatSubmit;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.PaperVoteInsertBo;
import org.housing.system.domain.bo.SysExportOwnerVoteInfoBo;
import org.housing.system.domain.bo.SysVoteBo;
import org.housing.system.domain.bo.SysVoteStatisticsExportBo;
import org.housing.system.domain.vo.SysVoteVo;
import org.housing.system.service.ISysVoteService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 投票
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/vote")
public class SysVoteController extends BaseController {

    private final ISysVoteService sysVoteService;

    /**
     * 查询投票列表
     */
    @SaCheckPermission("system:topic:list")
    @GetMapping("/list")
    public TableDataInfo<SysVoteVo> list(SysVoteBo bo, PageQuery pageQuery) {
        return sysVoteService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取投票详细信息
     *
     * @param voteId 主键
     */
    @SaCheckPermission("system:topic:query")
    @GetMapping("/{voteId}")
    public R<SysVoteVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long voteId) {
        return R.ok(sysVoteService.queryById(voteId));
    }

    /**
     * 新增投票
     */
    @SaCheckPermission("system:topic:add")
    @Log(title = "投票", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysVoteBo bo) {
        return toAjax(sysVoteService.insertByBo(bo));
    }

    /**
     * 修改投票
     */
    @SaCheckPermission("system:topic:edit")
    @Log(title = "投票", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysVoteBo bo) {
        return toAjax(sysVoteService.updateByBo(bo));
    }

    /**
     * 删除投票
     *
     * @param voteIds 主键串
     */
    @SaCheckPermission("system:topic:remove")
    @Log(title = "投票", businessType = BusinessType.DELETE)
    @DeleteMapping("/{voteIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] voteIds) {
        return toAjax(sysVoteService.deleteWithValidByIds(List.of(voteIds), true));
    }

    /**
     * 导出投票统计
     */
    @Log(title = "投票", businessType = BusinessType.EXPORT)
    @PostMapping("/exportStatistics")
    public void exportStatistics(@Validated SysVoteStatisticsExportBo bo, HttpServletResponse response) {
        sysVoteService.exportStatistics(bo, response);
    }

    /**
     * 后台图表数据
     *
     * @param bo 查询条件
     */
    @GetMapping("/charts")
    public R<JSONObject> getVoteCharts(SysVoteBo bo) {
        return R.ok(sysVoteService.getVoteCharts(bo.getHousingId(), bo.getTopicId(), bo.getVoteId()));
    }

    /**
     * 导出业主投票详情
     *
     * @param bo 参数
     * @param response
     */
    @Log(title = "投票", businessType = BusinessType.EXPORT)
    @PostMapping("/exportOwnerVoteInfo")
    public void ownerVoteInfo(@Validated SysExportOwnerVoteInfoBo bo, HttpServletResponse response) {
        sysVoteService.exportOwnerVoteInfo(bo.getVoteIds(), response);
    }

    /**
     * 纸质投票
     *
     * @param bo 参数
     */
    @SaCheckPermission("system:topic:add")
    @Log(title = "纸质投票", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/paperVoteInsert")
    public R<Void> paperVoteInsert(@Validated @RequestBody PaperVoteInsertBo bo) {
        sysVoteService.paperVoteInsert(bo);
        return R.ok();
    }
}
