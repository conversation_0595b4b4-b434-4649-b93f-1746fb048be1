package org.housing.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.mybatis.core.domain.BaseEntity;
import org.housing.system.domain.SysSmsChannel;

/**
 * 短信渠道业务对象 sys_sms_channel
 *
 * <AUTHOR>
 * @date 2025-03-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysSmsChannel.class, reverseConvertGenerate = false)
public class SysSmsChannelBo extends BaseEntity {

    /**
     * 短信渠道id
     */
    @NotNull(message = "短信渠道id不能为空", groups = { EditGroup.class })
    private Long channelId;

    /**
     * 渠道编号
     */
    @NotBlank(message = "渠道编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelCode;

    /**
     * 渠道签名
     */
    @NotBlank(message = "渠道签名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelSign;

    /**
     * 渠道appId
     */
    @NotBlank(message = "渠道appId不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelAppId;

    /**
     * 渠道secret
     */
    @NotBlank(message = "渠道secret不能为空", groups = { AddGroup.class, EditGroup.class })
    private String channelSecret;


}
