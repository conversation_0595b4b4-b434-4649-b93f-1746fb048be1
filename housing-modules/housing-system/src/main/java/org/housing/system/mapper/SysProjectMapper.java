package org.housing.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.housing.common.mybatis.core.mapper.BaseMapperPlus;
import org.housing.system.domain.SysProject;
import org.housing.system.domain.vo.SysProjectVo;

import java.util.List;

/**
 * 项目工程Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Mapper
public interface SysProjectMapper extends BaseMapperPlus<SysProject, SysProjectVo> {

    Page<SysProjectVo> selectPageProjectList(@Param("page") Page<SysProject> page, @Param(Constants.WRAPPER) Wrapper<SysProject> queryWrapper);

    List<SysProjectVo> selectProjectList(@Param(Constants.WRAPPER) Wrapper<SysProject> queryWrapper);

    SysProjectVo selectProjectById(Long projectId);
}
