package org.housing.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.model.LoginUser;
import org.housing.common.core.exception.ServiceException;
import org.housing.common.core.utils.MapstructUtils;
import org.housing.common.core.utils.StreamUtils;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.satoken.utils.LoginHelper;
import org.housing.system.domain.SysQuestion;
import org.housing.system.domain.SysQuestionAnswerDetails;
import org.housing.system.domain.SysQuestionAnswers;
import org.housing.system.domain.SysQuestionItems;
import org.housing.system.domain.bo.SysOwnerAnswerBo;
import org.housing.system.domain.bo.SysQuestionAnswerDetailsBo;
import org.housing.system.domain.bo.SysQuestionAnswersBo;
import org.housing.system.domain.vo.SysQuestionAnswersVo;
import org.housing.system.mapper.SysQuestionAnswerDetailsMapper;
import org.housing.system.mapper.SysQuestionAnswersMapper;
import org.housing.system.mapper.SysQuestionItemsMapper;
import org.housing.system.mapper.SysQuestionMapper;
import org.housing.system.service.ISysQuestionAnswersService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 业主答卷主Service业务层处理
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@RequiredArgsConstructor
@Service
public class SysQuestionAnswersServiceImpl implements ISysQuestionAnswersService {

    private final SysQuestionAnswersMapper baseMapper;

    private final SysQuestionMapper questionMapper;

    private final SysQuestionItemsMapper questionItemsMapper;

    private final SysQuestionAnswerDetailsMapper answerDetailsMapper;

    /**
     * 查询业主答卷主
     *
     * @param answersId 主键
     * @return 业主答卷主
     */
    @Override
    public SysQuestionAnswersVo queryById(Long answersId){
        return baseMapper.selectVoById(answersId);
    }

    /**
     * 分页查询业主答卷主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 业主答卷主分页列表
     */
    @Override
    public TableDataInfo<SysQuestionAnswersVo> queryPageList(SysQuestionAnswersBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysQuestionAnswers> lqw = buildQueryWrapper(bo);
        Page<SysQuestionAnswersVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的业主答卷主列表
     *
     * @param bo 查询条件
     * @return 业主答卷主列表
     */
    @Override
    public List<SysQuestionAnswersVo> queryList(SysQuestionAnswersBo bo) {
        LambdaQueryWrapper<SysQuestionAnswers> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysQuestionAnswers> buildQueryWrapper(SysQuestionAnswersBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysQuestionAnswers> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getQuestionId() != null, SysQuestionAnswers::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getHousingId() != null, SysQuestionAnswers::getHousingId, bo.getHousingId());
        lqw.eq(bo.getOwnerId() != null, SysQuestionAnswers::getOwnerId, bo.getOwnerId());
        return lqw;
    }

    /**
     * 新增业主答卷主
     *
     * @param bo 业主答卷主
     * @return 是否新增成功
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean insertByBo(SysOwnerAnswerBo bo) {
        boolean exists = baseMapper.exists(Wrappers.<SysQuestionAnswers>lambdaQuery().eq(SysQuestionAnswers::getQuestionId, bo.getQuestionId()).eq(SysQuestionAnswers::getOwnerId, bo.getOwnerId()));
        if (exists) {
            throw new ServiceException("问卷已填写");
        }
        SysQuestion sysQuestion = questionMapper.selectById(bo.getQuestionId());
        if (sysQuestion == null) {
            throw new ServiceException("问卷主键错误，问卷不存在");
        }
        Date now = new Date();
        if (now.before(sysQuestion.getQuestionStartTime()) || now.after(sysQuestion.getQuestionEndTime())) {
            throw new ServiceException("当前时间不在问卷的有效投票时间内");
        }
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (!loginUser.getHousingId().equals(sysQuestion.getHousingId())) {
            throw new ServiceException("问卷不属于当前小区");
        }
        if (sysQuestion.getSignature() == 1 && bo.getESignImg() == null) {
            throw new ServiceException("问卷需要电子签名");
        }
        List<SysQuestionItems> sysQuestionItems = questionItemsMapper.selectByIds(bo.getAnswers().keySet());
        if (sysQuestionItems.size() != bo.getAnswers().size()) {
            throw new ServiceException("问卷题目主键有错误");
        }
        boolean match = sysQuestionItems.stream().anyMatch(item -> !item.getQuestionId().equals(bo.getQuestionId()));
        if (match) {
            throw new ServiceException("问卷题目存在不属于当前问卷的数据");
        }
        SysQuestionAnswers add = new SysQuestionAnswers();
        add.setQuestionId(bo.getQuestionId());
        add.setHousingId(sysQuestion.getHousingId());
        add.setOwnerId(loginUser.getUserId());
        add.setESignImg(bo.getESignImg());
        add.setTenantId(loginUser.getTenantId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            List<SysQuestionAnswerDetails> detailsInsert = new ArrayList<>();
            Map<Long, SysQuestionItems> itemsMap = StreamUtils.toIdentityMap(sysQuestionItems, SysQuestionItems::getQuestionItemsId);
            for (Map.Entry<Long, String> entry : bo.getAnswers().entrySet()) {
                SysQuestionItems questionItems = itemsMap.get(entry.getKey());

                SysQuestionAnswerDetails details = new SysQuestionAnswerDetails();
                details.setAnswerId(add.getAnswersId());
                details.setItemId(entry.getKey());
                if (questionItems.getItemType() == 1 || questionItems.getItemType() == 3) {
                    details.setOptionAnswer(entry.getValue());
                } else {
                    details.setTextAnswer(entry.getValue());
                }
                detailsInsert.add(details);
            }
            answerDetailsMapper.insertBatch(detailsInsert);
        }
        return flag;
    }
}
