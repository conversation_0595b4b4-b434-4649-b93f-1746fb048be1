package org.housing.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.R;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.idempotent.annotation.RepeatSubmit;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.SysOwnerHouseBo;
import org.housing.system.domain.vo.SysOwnerHouseVo;
import org.housing.system.service.ISysOwnerHouseService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 业主房源
 *
 * <AUTHOR> Bin
 * @date 2025-01-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/ownerHouse")
public class SysOwnerHouseController extends BaseController {

    private final ISysOwnerHouseService sysOwnerHouseService;

    /**
     * 查询业主房源列表
     */
    @SaCheckPermission("system:owner:list")
    @GetMapping("/list")
    public TableDataInfo<SysOwnerHouseVo> list(SysOwnerHouseBo bo, PageQuery pageQuery) {
        return sysOwnerHouseService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取业主房源详细信息
     *
     * @param ownerHouseId 主键
     */
    @SaCheckPermission("system:owner:query")
    @GetMapping("/{ownerHouseId}")
    public R<SysOwnerHouseVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long ownerHouseId) {
        return R.ok(sysOwnerHouseService.queryById(ownerHouseId));
    }

    /**
     * 新增业主房源
     */
    @SaCheckPermission("system:owner:add")
    @Log(title = "业主房源", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysOwnerHouseBo bo) {
        return toAjax(sysOwnerHouseService.insertByBo(bo));
    }

    /**
     * 修改业主房源
     */
    @SaCheckPermission("system:owner:edit")
    @Log(title = "业主房源", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysOwnerHouseBo bo) {
        return toAjax(sysOwnerHouseService.updateByBo(bo));
    }

    /**
     * 删除业主房源
     *
     * @param ownerHouseIds 主键串
     */
    @SaCheckPermission("system:owner:remove")
    @Log(title = "业主房源", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ownerHouseIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ownerHouseIds) {
        return toAjax(sysOwnerHouseService.deleteWithValidByIds(List.of(ownerHouseIds), true));
    }
}
