package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 议题对象 sys_topic
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_topic")
public class SysTopic extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 议题主键
     */
    @TableId(value = "topic_id")
    private Long topicId;

    /**
     * 议题名称
     */
    private String topicName;

    /**
     * 议题文件
     */
    private String file;

    /**
     * 小区主键
     */
    private Long housingId;

    /**
     * 议题开始时间
     */
    private Date topicStartTime;

    /**
     * 议题结束时间
     */
    private Date topicEndTime;

    /**
     * 议题选项
     */
    private String topicOptions;

    /**
     * 投票项数量
     */
    private Integer votes;
}
