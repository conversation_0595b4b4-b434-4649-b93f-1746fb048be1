package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 问卷题目对象 sys_question_items
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_question_items")
public class SysQuestionItems extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 问卷题目ID
     */
    @TableId(value = "question_items_id")
    private Long questionItemsId;

    /**
     * 关联问卷主表ID
     */
    private Long questionId;

    /**
     * 题目类型 1-选择题 2-填空题
     */
    private Long itemType;

    /**
     * 题目内容
     */
    private String content;

    /**
     * 题目排序
     */
    private Long sort;

    /**
     * 是否必填 0-否 1-是
     */
    private Long isRequired;


}
