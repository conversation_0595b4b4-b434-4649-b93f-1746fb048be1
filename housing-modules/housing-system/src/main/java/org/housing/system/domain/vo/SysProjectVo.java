package org.housing.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.housing.common.excel.annotation.ExcelDictFormat;
import org.housing.common.excel.convert.ExcelDictConvert;
import org.housing.common.translation.annotation.Translation;
import org.housing.common.translation.constant.TransConstant;
import org.housing.system.domain.SysProject;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 项目工程视图对象 sys_project
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysProject.class)
public class SysProjectVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long projectId;

    /**
     * 公告ID
     */
    private Long noticeId;

    /**
     * 公告标题
     */
    @ExcelProperty(value = "公告标题")
    private String noticeTitle;

    /**
     * 公告内容
     */
    @ExcelProperty(value = "公告内容")
    private String noticeContent;

    /**
     * 项目编号
     */
    @ExcelProperty(value = "项目编号")
    private String projectNo;

    /**
     * 项目类型
     */
    @ExcelProperty(value = "项目类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_project_type")
    private String projectType;

    /**
     * 小区名称
     */
    @ExcelProperty(value = "小区名称")
    private String housingName;

    /**
     * 小区面积
     */
    @ExcelProperty(value = "小区面积")
    private String housingSize;

    /**
     * 物业费
     */
    @ExcelProperty(value = "物业费")
    private String propertyCost;

    /**
     * 报名费
     */
    @ExcelProperty(value = "报名费")
    private Long entryFee;

    /**
     * 保证金
     */
    @ExcelProperty(value = "保证金")
    private Long securityFee;

    /**
     * 报名开始时间
     */
    @ExcelProperty(value = "报名开始时间")
    private Date entryStartTime;

    /**
     * 报名结束时间
     */
    @ExcelProperty(value = "报名结束时间")
    private Date entryEndTime;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 投票状态
     */
    private String entryStatus;

    /**
     * 开标时间
     */
    private Date tendersTime;

    /**
     * 开标地点
     */
    private String tendersAddress;

    /**
     * 招标单位
     */
    private String tenderer;

    /**
     * 招标联系人手机
     */
    private String tendererPhone;

    /**
     * 招标联系人
     */
    private String tendererManage;

    /**
     * 招标类型名称
     */
    @Translation(type = TransConstant.DICT_TYPE_TO_LABEL, other = "sys_project_type", mapper = "projectType")
    private String projectTypeName;
}
