package org.housing.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.tenant.core.TenantEntity;
import org.housing.system.domain.SysOwner;

/**
 * 业主业务对象 sys_owner
 *
 * <AUTHOR>
 * @date 2024-09-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysOwner.class, reverseConvertGenerate = false)
public class SysOwnerBo extends TenantEntity {

    /**
     * 业主主键
     */
    @NotNull(message = "业主主键不能为空", groups = { EditGroup.class })
    private Long ownerId;

    /**
     * 小区id
     */
    @NotNull(message = "业主主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long housingId;

    /**
     * 业主名
     */
    @NotBlank(message = "业主名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ownerName;

    /**
     * 手机号码
     */
    @NotBlank(message = "手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String ownerPhone;

    /**
     * 面积
     */
    @NotBlank(message = "面积不能为空", groups = { AddGroup.class })
    private String acreage;

    /**
     * 组团
     */
    private String cluster;

    /**
     * 楼幢
     */
    @NotBlank(message = "楼幢不能为空", groups = { AddGroup.class })
    private String building;

    /**
     * 单元
     */
    @NotBlank(message = "单元不能为空", groups = { AddGroup.class, })
    private String cell;

    /**
     * 房号
     */
    @NotBlank(message = "房号不能为空", groups = { AddGroup.class })
    private String room;

    /**
     * 房产证正面
     */
    private String homeLicenseZ;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 审核状态
     */
    private String audit;

    /**
     * jsCode 小程序注册用
     */
    private String jsCode;

    /**
     * 前端不需要传 服务端填充用
     */
    private String openId;

    /**
     * 身份证正面
     */
    private String idCardZ;

    /**
     * 身份证反面
     */
    private String idCardF;

    /**
     * 业主备用号码
     */
    private String ownerBackPhone;
}
