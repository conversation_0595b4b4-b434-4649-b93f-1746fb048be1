package org.housing.system.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.housing.common.mybatis.core.mapper.BaseMapperPlus;
import org.housing.system.domain.SysVote;
import org.housing.system.domain.vo.SysOwnerVoteInfoVo;
import org.housing.system.domain.vo.SysVoteVo;

import java.util.List;

/**
 * 投票Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface SysVoteMapper extends BaseMapperPlus<SysVote, SysVoteVo> {

    List<SysOwnerVoteInfoVo> selectOwnerVoteInfoList(List<Long> voteIds);
}
