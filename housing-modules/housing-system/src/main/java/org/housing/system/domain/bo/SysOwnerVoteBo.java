package org.housing.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.mybatis.core.domain.BaseEntity;
import org.housing.system.domain.SysOwnerVote;

import java.util.List;

/**
 * 业主投票业务对象 sys_owner_vote
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysOwnerVote.class, reverseConvertGenerate = false)
public class SysOwnerVoteBo extends BaseEntity {

    /**
     * 业主投票主键
     */
    @NotNull(message = "业主投票主键不能为空", groups = { EditGroup.class })
    private Long ownerVoteId;

    /**
     * 业主主键
     */
    private List<Long> ownerIds;

    /**
     * 小区主键
     */
    private Long housingId;

    /**
     * 业主名
     */
    private String ownerName;

    /**
     * 业主手机
     */
    private String ownerPhone;

    /**
     * 投票主键
     */
    @NotNull(message = "投票主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long voteId;

    /**
     * 意见
     */
    @NotBlank(message = "意见不能为空", groups = { AddGroup.class, EditGroup.class })
    private String advice;

    /**
     * 备注
     */
    private String remark;

    /**
     * 来源，0-线下，1-小程序，2-电话
     */
    private String resource;

    /**
     * 业主主键
     */
    private Long ownerId;
}
