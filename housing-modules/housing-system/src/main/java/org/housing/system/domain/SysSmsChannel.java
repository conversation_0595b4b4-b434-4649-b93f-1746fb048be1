package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 短信渠道对象 sys_sms_channel
 *
 * <AUTHOR> Bin
 * @date 2025-03-13
 */
@Data
@TableName("sys_sms_channel")
public class SysSmsChannel implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 短信渠道id
     */
    @TableId(value = "channel_id")
    private Long channelId;

    /**
     * 渠道编号
     */
    private String channelCode;

    /**
     * 渠道签名
     */
    private String channelSign;

    /**
     * 渠道appId
     */
    private String channelAppId;

    /**
     * 渠道secret
     */
    private String channelSecret;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 租户编号
     */
    private String tenantId;
}
