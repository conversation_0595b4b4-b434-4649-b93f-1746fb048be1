package org.housing.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.R;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.excel.core.ExcelResult;
import org.housing.common.excel.utils.ExcelUtil;
import org.housing.common.idempotent.annotation.RepeatSubmit;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.SysTopicBo;
import org.housing.system.domain.bo.SysTopicExportBo;
import org.housing.system.domain.vo.SysTopicGovImportVo;
import org.housing.system.domain.vo.SysTopicPhoneImportVo;
import org.housing.system.domain.vo.SysTopicVo;
import org.housing.system.listener.SysTopicGovImportListener;
import org.housing.system.listener.SysTopicPhoneImportListener;
import org.housing.system.service.ISysTopicService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * 议题
 *
 * <AUTHOR> Bin
 * @date 2024-12-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/topic")
public class SysTopicController extends BaseController {

    private final ISysTopicService sysTopicService;

    /**
     * 查询议题列表
     */
    @SaCheckPermission("system:topic:list")
    @GetMapping("/list")
    public TableDataInfo<SysTopicVo> list(SysTopicBo bo, PageQuery pageQuery) {
        return sysTopicService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取议题详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:topic:query")
    @GetMapping("/{id}")
    public R<SysTopicVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(sysTopicService.queryById(id));
    }

    /**
     * 新增议题
     */
    @SaCheckPermission("system:topic:add")
    @Log(title = "议题", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysTopicBo bo) {
        return toAjax(sysTopicService.insertByBo(bo));
    }

    /**
     * 修改议题
     */
    @SaCheckPermission("system:topic:edit")
    @Log(title = "议题", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysTopicBo bo) {
        return toAjax(sysTopicService.updateByBo(bo));
    }

    /**
     * 删除议题
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:topic:remove")
    @Log(title = "议题", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sysTopicService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 导出议题列表
     */
    @SaCheckPermission("system:topic:export")
    @Log(title = "议题", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(@Validated SysTopicExportBo bo, HttpServletResponse response) {
        sysTopicService.export(bo, response);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "电话投票数据", SysTopicPhoneImportVo.class, response);
    }

    /**
     * 导入数据
     *
     * @param file 导入文件
     */
    @Log(title = "议题", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:topic:add")
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importData(@RequestPart("file") MultipartFile file) throws Exception {
        ExcelResult<SysTopicPhoneImportVo> result = ExcelUtil.importExcel(file.getInputStream(), SysTopicPhoneImportVo.class, new SysTopicPhoneImportListener());
        return R.ok(result.getAnalysis());
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importGovTemplate")
    public void importGovTemplate(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "政府卡投票数据", SysTopicGovImportVo.class, response);
    }

    /**
     * 导入数据
     *
     * @param file 导入文件
     */
    @Log(title = "议题", businessType = BusinessType.IMPORT)
    @SaCheckPermission("system:topic:add")
    @PostMapping(value = "/importGovData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> importGovData(@RequestPart("file") MultipartFile file) throws Exception {
        ExcelResult<SysTopicGovImportVo> result = ExcelUtil.importExcel(file.getInputStream(), SysTopicGovImportVo.class, new SysTopicGovImportListener());
        return R.ok(result.getAnalysis());
    }

}
