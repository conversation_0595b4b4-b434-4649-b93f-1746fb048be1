package org.housing.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.domain.R;
import org.housing.common.excel.utils.ExcelUtil;
import org.housing.common.log.annotation.Log;
import org.housing.common.log.enums.BusinessType;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.web.core.BaseController;
import org.housing.system.domain.bo.MassTextingBo;
import org.housing.system.domain.bo.SysNoticeBo;
import org.housing.system.domain.vo.SysNoticeVo;
import org.housing.system.service.ISysNoticeService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 公告 信息操作处理
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/notice")
public class SysNoticeController extends BaseController {

    private final ISysNoticeService noticeService;

    /**
     * 获取通知公告列表
     */
    @SaCheckPermission("system:notice:list")
    @GetMapping("/list")
    public TableDataInfo<SysNoticeVo> list(SysNoticeBo notice, PageQuery pageQuery) {
        return noticeService.selectPageNoticeList(notice, pageQuery);
    }

    /**
     * 查询公告下拉列表
     */
    @SaCheckPermission("system:notice:list")
    @GetMapping("/noticeSelect")
    public R<List<SysNoticeVo>> noticeSelect() {
        return R.ok(noticeService.selectNoticeSelect());
    }

    /**
     * 根据通知公告编号获取详细信息
     *
     * @param noticeId 公告ID
     */
    @SaCheckPermission("system:notice:query")
    @GetMapping(value = "/{noticeId}")
    public R<SysNoticeVo> getInfo(@PathVariable Long noticeId) {
        return R.ok(noticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知公告
     */
    @SaCheckPermission("system:notice:add")
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody SysNoticeBo notice) {
        int rows = noticeService.insertNotice(notice);
        if (rows <= 0) {
            return R.fail();
        }
        return R.ok();
    }

    /**
     * 修改通知公告
     */
    @SaCheckPermission("system:notice:edit")
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody SysNoticeBo notice) {
        return toAjax(noticeService.updateNotice(notice));
    }

    /**
     * 删除通知公告
     *
     * @param noticeIds 公告ID串
     */
    @SaCheckPermission("system:notice:remove")
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{noticeIds}")
    public R<Void> remove(@PathVariable Long[] noticeIds) {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }

    /**
     * 导出公告
     *
     * @param bo 查询内容
     */
    @SaCheckPermission("system:notice:list")
    @Log(title = "通知公告", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysNoticeBo bo, HttpServletResponse response) {
        List<SysNoticeVo> sysNoticeVos = noticeService.selectNoticeList(bo);
        ExcelUtil.exportExcel(sysNoticeVos, "公告", SysNoticeVo.class, response);
    }

    /**
     * 群发通知业主
     *
     * @param massTextingBo 参数
     */
    @SaCheckPermission("system:notice:add")
    @PostMapping("/massTexting")
    public R<Void> massTexting(@Validated @RequestBody MassTextingBo massTextingBo) {
        noticeService.massTexting(massTextingBo);
        return R.ok();
    }
}
