package org.housing.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.housing.common.translation.annotation.Translation;
import org.housing.common.translation.constant.TransConstant;
import org.housing.system.domain.SysQuestion;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;



/**
 * 问卷管理视图对象 sys_question
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysQuestion.class)
public class SysQuestionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 问卷id
     */
    private Long questionId;

    /**
     * 问卷标题
     */
    private String questionTitle;

    /**
     * 问卷描述
     */
    private String questionRemark;

    /**
     * 是否需要签名
     */
    private Long signature;

    /**
     * 关联的小区id
     */
    private Long housingId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 问卷题目列表
     */
    private List<SysQuestionItemsVo> questionItems;

    /**
     * 小区名称
     */
    @Translation(type = TransConstant.HOUSING_ID_TO_NAME, mapper = "housingId")
    private String housingName;

    /**
     * 题目选项 英文逗号隔开
     */
    @JsonIgnore
    private String options;

    /**
     * 题目选项
     */
    private List<SysQuestionOptionsVo> questionOptions;

    /**
     * 是否已投票 0-未投票 1-已投票 (仅app端使用)
     */
    private Integer isVoted;

    /**
     * 问卷开始时间
     */
    private Date questionStartTime;

    /**
     * 问卷结束时间
     */
    private Date questionEndTime;
}
