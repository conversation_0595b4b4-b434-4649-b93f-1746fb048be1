package org.housing.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.zxing.BarcodeFormat;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.AltChunkType;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.ObjectFactory;
import org.docx4j.wml.P;
import org.docx4j.wml.R;
import org.housing.common.core.exception.ServiceException;
import org.housing.common.core.utils.MapstructUtils;
import org.housing.common.core.utils.SpringUtils;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.core.utils.file.FileUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.system.domain.*;
import org.housing.system.domain.bo.SysTopicBo;
import org.housing.system.domain.bo.SysTopicExportBo;
import org.housing.system.domain.vo.SysOssVo;
import org.housing.system.domain.vo.SysOwnerHouseVo;
import org.housing.system.domain.vo.SysOwnerVo;
import org.housing.system.domain.vo.SysTopicVo;
import org.housing.system.mapper.*;
import org.housing.system.service.ISysOssService;
import org.housing.system.service.ISysOwnerHouseService;
import org.housing.system.service.ISysTopicService;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 议题Service业务层处理
 *
 * <AUTHOR> Bin
 * @date 2024-12-03
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SysTopicServiceImpl implements ISysTopicService {

    private final SysTopicMapper baseMapper;

    private final SysHousingMapper housingMapper;

    private final SysOwnerMapper ownerMapper;

    private final SysVoteMapper voteMapper;

    private final SysOwnerVoteMapper ownerVoteMapper;

    private final ISysOwnerHouseService ownerHouseService;

    /**
     * 查询议题
     *
     * @param id 主键
     * @return 议题
     */
    @Override
    public SysTopicVo queryById(Long id){
        SysTopicVo sysTopicVo = baseMapper.selectVoById(id);
        ISysOssService ossService = SpringUtils.getBean(ISysOssService.class);
        List<SysOssVo> sysOssVos = ossService.listByIds(Arrays.stream(sysTopicVo.getFile().split(",")).map(Long::parseLong).toList());
        sysTopicVo.setFiles(sysOssVos);
        return sysTopicVo;
    }

    /**
     * 分页查询议题列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 议题分页列表
     */
    @Override
    public TableDataInfo<SysTopicVo> queryPageList(SysTopicBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysTopic> lqw = buildQueryWrapper(bo);
        Page<SysTopicVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        if (CollUtil.isNotEmpty(result.getRecords())) {
            ISysOssService ossService = SpringUtils.getBean(ISysOssService.class);
            result.getRecords().forEach(x -> {
                List<SysOssVo> sysOssVos = ossService.listByIds(Arrays.stream(x.getFile().split(",")).map(Long::parseLong).toList());
                x.setFiles(sysOssVos);
            });
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的议题列表
     *
     * @param bo 查询条件
     * @return 议题列表
     */
    @Override
    public List<SysTopicVo> queryList(SysTopicBo bo) {
        LambdaQueryWrapper<SysTopic> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysTopic> buildQueryWrapper(SysTopicBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysTopic> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTopicName()), SysTopic::getTopicName, bo.getTopicName());
        lqw.eq(StringUtils.isNotBlank(bo.getFile()), SysTopic::getFile, bo.getFile());
        lqw.and(StringUtils.isNotBlank(bo.getHousingName()), x -> {
            List<Long> housingIds = housingMapper.selectList(new LambdaQueryWrapper<SysHousing>()
                .select(SysHousing::getHousingId)
                .like(SysHousing::getHousingName, bo.getHousingName())
            ).stream().map(SysHousing::getHousingId).toList();
            x.in(SysTopic::getHousingId, housingIds);
        });
        lqw.eq(bo.getHousingId() != null, SysTopic::getHousingId, bo.getHousingId());
        lqw.gt(bo.getTopicStartTime() != null, SysTopic::getTopicStartTime, bo.getTopicStartTime());
        lqw.lt(bo.getTopicEndTime() != null, SysTopic::getTopicEndTime, bo.getTopicEndTime());
        lqw.eq(StringUtils.isNotBlank(bo.getTopicOptions()), SysTopic::getTopicOptions, bo.getTopicOptions());
        return lqw;
    }

    /**
     * 新增议题
     *
     * @param bo 议题
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysTopicBo bo) {
        SysTopic add = MapstructUtils.convert(bo, SysTopic.class);
        validEntityBeforeSave(add);
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改议题
     *
     * @param bo 议题
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysTopicBo bo) {
        SysTopic update = MapstructUtils.convert(bo, SysTopic.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysTopic entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除议题信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @SneakyThrows
    @Override
    public void export(SysTopicExportBo bo, HttpServletResponse response) {
        List<SysTopic> sysTopics = baseMapper.selectByIds(bo.getTopicIds());
        if (CollUtil.isEmpty(sysTopics)) {
            throw new ServiceException("议题不存在");
        }
        Set<Long> housingIds = sysTopics.stream().map(SysTopic::getHousingId).collect(Collectors.toSet());
        if (housingIds.size() > 1) {
            throw new ServiceException("暂不支持不同小区的投票导出");
        }
        List<SysOwnerVo> sysOwnerVos = ownerMapper.selectVoList(new LambdaQueryWrapper<SysOwner>().eq(SysOwner::getHousingId, sysTopics.get(0).getHousingId()).eq(SysOwner::getAudit, "1"));
        if (CollUtil.isEmpty(sysOwnerVos)) {
            throw new ServiceException("先添加小区业主信息");
        }
        // 设置HTTP响应的头信息
        String fileName = "表决票.docx";
        FileUtils.setAttachmentResponseHeader(response, fileName);
        response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=UTF-8");

        List<Map<String, Object>> assemblyData = assemblyData(sysTopics, sysOwnerVos);
        // 配置FreeMarker
        TemplateEngine engine = TemplateUtil.createEngine(new TemplateConfig("/", TemplateConfig.ResourceMode.CLASSPATH));
        Template template = engine.getTemplate("template/world-template2.ftl");
        // 创建一个新的DOCX文档
        WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.createPackage();
        StringWriter stringWriter = new StringWriter();
        template.render(Map.of("assemblyData", assemblyData), stringWriter);
        String xmlContent = stringWriter.toString();
        MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
        documentPart.addAltChunk(AltChunkType.Xml, xmlContent.getBytes(StandardCharsets.UTF_8));
        // 使用 ByteArrayOutputStream 来保存文档
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            wordMLPackage.save(baos);
            log.info("文档生成成功，大小：{} 字节", baos.size());
            // 将 ByteArrayOutputStream 的内容写入响应
            baos.writeTo(response.getOutputStream());
            log.info("文档写入响应成功");
        } catch (Exception e) {
            log.error("保存或写入文档时发生错误", e);
            throw new ServiceException("导出文档失败：" + e.getMessage());
        }

    }

    private List<Map<String, Object>> assemblyData(List<SysTopic> sysTopics, List<SysOwnerVo> sysOwnerVos) {
        List<Map<String, Object>> list = CollUtil.newArrayList();
        List<SysVote> votesByTopic = voteMapper.selectList(new LambdaQueryWrapper<SysVote>().in(SysVote::getTopicId, sysTopics.stream().map(SysTopic::getTopicId).toList()));
        Map<Long, List<SysVote>> voteMap = votesByTopic.stream().collect(Collectors.groupingBy(SysVote::getTopicId));
        if (sysTopics.size() > voteMap.size()) {
            throw new ServiceException("议题数据异常，有议题没有投票项");
        }
        List<SysOwnerVote> sysOwnerVotes = ownerVoteMapper.selectList(new LambdaQueryWrapper<SysOwnerVote>()
            .in(SysOwnerVote::getVoteId, votesByTopic.stream().map(SysVote::getVoteId).toList())
            .in(SysOwnerVote::getOwnerId, sysOwnerVos.stream().map(SysOwnerVo::getOwnerId).toList()));
        Map<Long, List<SysOwnerVote>> ownerVoteMap = sysOwnerVotes.stream().collect(Collectors.groupingBy(SysOwnerVote::getOwnerId));
        for (SysOwnerVo sysOwnerVo : sysOwnerVos) {
            if (ownerVoteMap.containsKey(sysOwnerVo.getOwnerId())) {
                continue;
            }
            BufferedImage image = QrCodeUtil.generate(sysOwnerVo.getOwnerId().toString(), BarcodeFormat.CODE_128, 120, 60);
            String barCode = ImgUtil.toBase64(image, "png");

            Map<String, Object> map = new HashMap<>();
            map.put("barCode", barCode);
            map.put("ownerId", sysOwnerVo.getOwnerId());

            List<SysOwnerHouseVo> sysOwnerHouseVos = ownerHouseService.selectByOwnerId(sysOwnerVo.getOwnerId());
            List<String> houseInfos = sysOwnerHouseVos.stream().map(sysOwnerHouseVo -> Optional.ofNullable(sysOwnerHouseVo.getCluster()).orElse("0") + "-" + sysOwnerHouseVo.getBuilding() + "-" + sysOwnerHouseVo.getCell() + "-" + sysOwnerHouseVo.getRoom()).toList();
            map.put("houseInfo", StrUtil.join("，", houseInfos));

            list.add(map);
        }
        return list;
    }

    private void addPageBreak(WordprocessingMLPackage wordMLPackage) {
        MainDocumentPart documentPart = wordMLPackage.getMainDocumentPart();
        ObjectFactory objectFactory = new ObjectFactory();
        P paragraph = objectFactory.createP();
        R run = objectFactory.createR();
        org.docx4j.wml.Br br = objectFactory.createBr();
        br.setType(org.docx4j.wml.STBrType.PAGE);
        run.getContent().add(br);
        paragraph.getContent().add(run);
        documentPart.addObject(paragraph);
    }
}
