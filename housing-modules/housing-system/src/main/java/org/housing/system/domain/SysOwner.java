package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 业主对象 sys_owner
 *
 * <AUTHOR>
 * @date 2024-09-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_owner")
public class SysOwner extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 业主主键
     */
    @TableId(value = "owner_id")
    private Long ownerId;

    /**
     * 小区id
     */
    private Long housingId;

    /**
     * 面积
     */
    private String acreage;

    /**
     * 楼幢
     */
    private String building;

    /**
     * 单元
     */
    private String cell;

    /**
     * 房号
     */
    private String room;

    /**
     * 联系人
     */
    private String ownerName;

    /**
     * 联系号码
     */
    private String ownerPhone;

    /**
     * 房产证正面
     */
    private String homeLicenseZ;

    /**
     * 身份证正面
     */
    private String idCardZ;

    /**
     * 身份证反面
     */
    private String idCardF;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核状态
     */
    private String audit;

    /**
     * 微信openId
     */
    private String openId;

    /**
     * 房源数
     */
    private Integer houseNum;

    /**
     * 业主备用号码
     */
    private String ownerBackPhone;
}
