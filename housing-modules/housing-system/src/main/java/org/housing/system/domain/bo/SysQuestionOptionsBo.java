package org.housing.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.common.mybatis.core.domain.BaseEntity;
import org.housing.common.tenant.core.TenantEntity;
import org.housing.system.domain.SysQuestionOptions;

/**
 * 选择题选项业务对象 sys_question_options
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SysQuestionOptions.class, reverseConvertGenerate = false)
public class SysQuestionOptionsBo extends BaseEntity {

    /**
     * 问卷题目选项ID
     */
    private Long optionsId;

    /**
     * 选项内容
     */
    @NotBlank(message = "选项内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String optionContent;

    /**
     * 选项排序
     */
    @NotNull(message = "选项排序不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sort;

    /**
     * 关联的小区id
     */
    @NotNull(message = "关联的小区id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long housingId;
}
