package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 项目工程对象 sys_project
 *
 * <AUTHOR>
 * @date 2024-09-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_project")
public class SysProject extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目ID
     */
    @TableId(value = "project_id")
    private Long projectId;

    /**
     * 公告ID
     */
    private Long noticeId;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 物业费
     */
    private String propertyCost;

    /**
     * 报名费
     */
    private Long entryFee;

    /**
     * 保证金
     */
    private Long securityFee;

    /**
     * 报名开始时间
     */
    private Date entryStartTime;

    /**
     * 报名结束时间
     */
    private Date entryEndTime;

    /**
     * 开标时间
     */
    private Date tendersTime;

    /**
     * 开标地点
     */
    private String tendersAddress;

    /**
     * 招标单位
     */
    private String tenderer;

    /**
     * 招标联系人手机
     */
    private String tendererPhone;

    /**
     * 招标联系人
     */
    private String tendererManage;


}
