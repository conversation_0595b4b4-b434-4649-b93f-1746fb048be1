package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 项目报名对象 sys_notice_entry
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@TableName("sys_project_entry")
public class SysProjectEntry implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 项目报名主键
     */
    @TableId(value = "project_entry_id")
    private Long projectEntryId;

    /**
     * 项目主键
     */
    private Long projectId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 信用代码
     */
    private String creditCode;

    /**
     * 联系人
     */
    private String manageUser;

    /**
     * 联系电话
     */
    private String phoneNumber;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 营业执照
     */
    private String license;

    /**
     * 法人授权委托书
     */
    private String powerBook;

    /**
     * 其他材料
     */
    private String other;

    /**
     * 0-未支付,1-支付中,2-已支付,3-支付失败
     */
    private String payStatus;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 审核状态 0-待审核 1-已审核
     */
    private String audits;

    /**
     * 报名人员主键
     */
    private Long entryUserId;

    /**
     * 身份证正面
     */
    private String idCardZ;

    /**
     * 身份证反面
     */
    private String idCardF;
}
