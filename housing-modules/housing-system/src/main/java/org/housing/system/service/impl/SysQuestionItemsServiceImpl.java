package org.housing.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.utils.MapstructUtils;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.system.domain.SysQuestionItems;
import org.housing.system.domain.bo.SysQuestionItemsBo;
import org.housing.system.domain.vo.SysQuestionItemsVo;
import org.housing.system.mapper.SysQuestionItemsMapper;
import org.housing.system.service.ISysQuestionItemsService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 问卷题目Service业务层处理
 *
 * <AUTHOR> Bin
 * @date 2025-07-12
 */
@RequiredArgsConstructor
@Service
public class SysQuestionItemsServiceImpl implements ISysQuestionItemsService {

    private final SysQuestionItemsMapper baseMapper;

    /**
     * 查询问卷题目
     *
     * @param questionItemsId 主键
     * @return 问卷题目
     */
    @Override
    public SysQuestionItemsVo queryById(Long questionItemsId){
        return baseMapper.selectVoById(questionItemsId);
    }

    /**
     * 分页查询问卷题目列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 问卷题目分页列表
     */
    @Override
    public TableDataInfo<SysQuestionItemsVo> queryPageList(SysQuestionItemsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysQuestionItems> lqw = buildQueryWrapper(bo);
        Page<SysQuestionItemsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的问卷题目列表
     *
     * @param bo 查询条件
     * @return 问卷题目列表
     */
    @Override
    public List<SysQuestionItemsVo> queryList(SysQuestionItemsBo bo) {
        LambdaQueryWrapper<SysQuestionItems> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysQuestionItems> buildQueryWrapper(SysQuestionItemsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysQuestionItems> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getQuestionId() != null, SysQuestionItems::getQuestionId, bo.getQuestionId());
        lqw.eq(bo.getItemType() != null, SysQuestionItems::getItemType, bo.getItemType());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), SysQuestionItems::getContent, bo.getContent());
        lqw.eq(bo.getSort() != null, SysQuestionItems::getSort, bo.getSort());
        lqw.eq(bo.getIsRequired() != null, SysQuestionItems::getIsRequired, bo.getIsRequired());
        return lqw;
    }

    /**
     * 新增问卷题目
     *
     * @param bo 问卷题目
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysQuestionItemsBo bo) {
        SysQuestionItems add = MapstructUtils.convert(bo, SysQuestionItems.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setQuestionItemsId(add.getQuestionItemsId());
        }
        return flag;
    }

    /**
     * 修改问卷题目
     *
     * @param bo 问卷题目
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysQuestionItemsBo bo) {
        SysQuestionItems update = MapstructUtils.convert(bo, SysQuestionItems.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysQuestionItems entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除问卷题目信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
