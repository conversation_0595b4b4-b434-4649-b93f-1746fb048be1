package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.housing.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * 投票对象 sys_vote
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_vote")
public class SysVote extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 投票主键
     */
    @TableId(value = "vote_id")
    private Long voteId;

    /**
     * 小区主键
     */
    private Long housingId;

    /**
     * 议题主键
     */
    private Long topicId;

    /**
     * 投票标题
     */
    private String voteTitle;

    /**
     * 投票数
     */
    private Integer voteCount;

    /**
     * 排序
     */
    private Integer voteSort;
}
