package org.housing.system.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 报名人员对象 sys_entry_user
 *
 * <AUTHOR> Bin
 * @date 2024-10-30
 */
@Data
@TableName("sys_entry_user")
public class SysEntryUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报名人员表主键
     */
    @TableId(value = "entry_user_id")
    private Long entryUserId;

    /**
     * 微信openid
     */
    private String openId;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 微信性别
     */
    private Integer sex;

    /**
     * 微信城市
     */
    private String city;

    /**
     * 微信省份
     */
    private String province;

    /**
     * 微信国家
     */
    private String country;

    /**
     * 微信头像
     */
    private String headImgUrl;

    /**
     * unionid
     */
    private String unionId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
