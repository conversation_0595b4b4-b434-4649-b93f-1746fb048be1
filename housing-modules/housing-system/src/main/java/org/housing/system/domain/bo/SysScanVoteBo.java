package org.housing.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.housing.common.core.validate.AddGroup;
import org.housing.system.domain.SysVote;

/**
 * 扫码添加
 *
 * <AUTHOR>
 * @since 2024-10-08
 */
@Data
@AutoMapper(target = SysVote.class, reverseConvertGenerate = false)
public class SysScanVoteBo {

    /**
     * 公告主键
     */
    @NotNull(message = "公告主键不能为空", groups = { AddGroup.class })
    private Long noticeId;

    /**
     * 业主主键
     */
    @NotNull(message = "业主主键不能为空", groups = { AddGroup.class })
    private Long ownerId;

    /**
     * 意见
     */
    @NotBlank(message = "意见不能为空", groups = { AddGroup.class })
    private String advice;

    /**
     * 备注
     */
    private String remark;
}
