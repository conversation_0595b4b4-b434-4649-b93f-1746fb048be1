package org.housing.system.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.housing.common.core.exception.ServiceException;
import org.housing.common.core.utils.StringUtils;
import org.housing.common.mybatis.core.page.PageQuery;
import org.housing.common.mybatis.core.page.TableDataInfo;
import org.housing.common.satoken.utils.LoginHelper;
import org.housing.system.domain.SysOwnerVote;
import org.housing.system.domain.SysVote;
import org.housing.system.domain.bo.SysOwnerVoteBo;
import org.housing.system.domain.vo.SysOwnerVoteVo;
import org.housing.system.mapper.SysOwnerMapper;
import org.housing.system.mapper.SysOwnerVoteMapper;
import org.housing.system.mapper.SysVoteMapper;
import org.housing.system.service.ISysOwnerVoteService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 业主投票Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class SysOwnerVoteServiceImpl implements ISysOwnerVoteService {

    private final SysOwnerVoteMapper baseMapper;

    private final SysVoteMapper voteMapper;

    private final SysOwnerMapper ownerMapper;

    /**
     * 查询业主投票
     *
     * @param ownerVoteId 主键
     * @return 业主投票
     */
    @Override
    public SysOwnerVoteVo queryById(Long ownerVoteId){
        return baseMapper.selectOwnerVoteById(ownerVoteId);
    }

    /**
     * 分页查询业主投票列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 业主投票分页列表
     */
    @Override
    public TableDataInfo<SysOwnerVoteVo> queryPageList(SysOwnerVoteBo bo, PageQuery pageQuery) {
        QueryWrapper<SysOwnerVote> lqw = buildQueryWrapper(bo);
        Page<SysOwnerVoteVo> result = baseMapper.selectOwnerVotePage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的业主投票列表
     *
     * @param bo 查询条件
     * @return 业主投票列表
     */
    @Override
    public List<SysOwnerVoteVo> queryList(SysOwnerVoteBo bo) {
        QueryWrapper<SysOwnerVote> lqw = buildQueryWrapper(bo);
        return baseMapper.selectOwnerVoteList(lqw);
    }

    private QueryWrapper<SysOwnerVote> buildQueryWrapper(SysOwnerVoteBo bo) {
        QueryWrapper<SysOwnerVote> query = Wrappers.query();
        query.like(StrUtil.isNotBlank(bo.getOwnerName()), "o.owner_name", bo.getOwnerName());
        query.like(StrUtil.isNotBlank(bo.getOwnerPhone()), "o.owner_phone", bo.getOwnerPhone());
        query.eq(bo.getVoteId() != null, "ov.vote_id", bo.getVoteId());
        query.eq(bo.getHousingId() != null, "o.housing_id", bo.getHousingId());
        query.eq(StringUtils.isNotBlank(bo.getAdvice()), "ov.advice", bo.getAdvice());
        query.orderByDesc("ov.create_time");
        return query;
    }

    /**
     * 新增业主投票
     *
     * @param bo 业主投票
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysOwnerVoteBo bo) {
        SysVote sysVote = voteMapper.selectById(bo.getVoteId());
        if (sysVote == null) {
            throw new ServiceException("投票不存在");
        }
        bo.setHousingId(sysVote.getHousingId());
        SysOwnerVote sysOwnerVote = baseMapper.selectOne(new LambdaQueryWrapper<SysOwnerVote>()
            .eq(SysOwnerVote::getVoteId, bo.getVoteId())
            .eq(SysOwnerVote::getOwnerId, LoginHelper.getUserId()));

        if (sysOwnerVote != null) {
            if (StrUtil.equals(sysOwnerVote.getResource(), "1") || StrUtil.equals(sysOwnerVote.getResource(), "3")) {
                throw new ServiceException("该业主已参与投票");
            }
            sysOwnerVote.setResource("1");
            sysOwnerVote.setAdvice(bo.getAdvice());
            sysOwnerVote.setCreateTime(bo.getCreateTime());
            return baseMapper.updateById(sysOwnerVote) > 0;
        }
        sysOwnerVote = new SysOwnerVote();
        sysOwnerVote.setOwnerId(LoginHelper.getUserId());
        sysOwnerVote.setVoteId(bo.getVoteId());
        sysOwnerVote.setAdvice(bo.getAdvice());
        sysOwnerVote.setResource(bo.getResource());
        sysOwnerVote.setRemark(bo.getRemark());

        boolean flag = baseMapper.insert(sysOwnerVote) > 0;
        if (flag) {
           sysVote.setVoteCount(sysVote.getVoteCount() + 1);
           voteMapper.updateById(sysVote);
        }

        return flag;
    }

    /**
     * 修改业主投票
     *
     * @param bo 业主投票
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysOwnerVoteBo bo) {
        SysOwnerVote sysOwnerVote = baseMapper.selectById(bo.getOwnerVoteId());
        if (sysOwnerVote == null) {
            throw new ServiceException("数据不存在");
        }
        sysOwnerVote.setAdvice(bo.getAdvice());
        sysOwnerVote.setResource(bo.getResource());
        return baseMapper.updateById(sysOwnerVote) > 0;
    }

    /**
     * 删除业主投票信息
     *
     * @param id     待删除的主键
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteById(Long id) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    public void phoneImportInsert(Long ownerId, Long voteId, String advice) {
        if (StrUtil.isBlank(advice)) {
            throw new ServiceException("投票意见不能为空");
        }
        SysOwnerVote sysOwnerVote = baseMapper.selectOne(new LambdaQueryWrapper<SysOwnerVote>()
            .eq(SysOwnerVote::getVoteId, voteId)
            .eq(SysOwnerVote::getOwnerId, ownerId));
        if (sysOwnerVote != null) {
            sysOwnerVote.setAdvice(advice);
            baseMapper.updateById(sysOwnerVote);
            return;
        }

        sysOwnerVote = new SysOwnerVote();
        sysOwnerVote.setOwnerId(ownerId);
        sysOwnerVote.setVoteId(voteId);
        sysOwnerVote.setAdvice(advice);
        sysOwnerVote.setResource("2");

        boolean flag = baseMapper.insert(sysOwnerVote) > 0;
        if (flag) {
            SysVote sysVote = voteMapper.selectById(voteId);
            sysVote.setVoteCount(sysVote.getVoteCount() + 1);
            voteMapper.updateById(sysVote);
        }
    }

    @Override
    public void govImportInsert(Long ownerId, Long voteId, String advice) {
        // 政府卡特殊，分两次导入，如果没有投票意见，则默认为-1 待提交意见
        if (StrUtil.isBlank(advice)) {
            advice = "-2";
        }
        SysOwnerVote sysOwnerVote = baseMapper.selectOne(new LambdaQueryWrapper<SysOwnerVote>()
            .eq(SysOwnerVote::getVoteId, voteId)
            .eq(SysOwnerVote::getOwnerId, ownerId));
        if (sysOwnerVote != null) {
            // 所有投过的都没有政府卡的优先级高
            sysOwnerVote.setResource("3");
            sysOwnerVote.setAdvice(advice);
            sysOwnerVote.setCreateTime(new Date());
            baseMapper.updateById(sysOwnerVote);
            return;
        }
        sysOwnerVote = new SysOwnerVote();
        sysOwnerVote.setOwnerId(ownerId);
        sysOwnerVote.setVoteId(voteId);
        sysOwnerVote.setAdvice(advice);
        sysOwnerVote.setResource("3");
        boolean flag = baseMapper.insert(sysOwnerVote) > 0;
        if (flag) {
            SysVote sysVote = voteMapper.selectById(voteId);
            sysVote.setVoteCount(sysVote.getVoteCount() + 1);
            voteMapper.updateById(sysVote);
        }
    }
}
