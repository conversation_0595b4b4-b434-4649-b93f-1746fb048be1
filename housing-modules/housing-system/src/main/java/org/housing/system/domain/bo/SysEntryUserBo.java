package org.housing.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.system.domain.SysEntryUser;

/**
 * 报名人员业务对象 sys_entry_user
 *
 * <AUTHOR> Bin
 * @date 2024-10-30
 */
@Data
@AutoMapper(target = SysEntryUser.class, reverseConvertGenerate = false)
public class SysEntryUserBo {

    /**
     * 报名人员表主键
     */
    @NotNull(message = "报名人员表主键不能为空", groups = { EditGroup.class })
    private Long entryUserId;

    /**
     * 微信openid
     */
    @NotBlank(message = "微信openid不能为空", groups = { AddGroup.class, EditGroup.class })
    private String openId;

    /**
     * 微信昵称
     */
    @NotBlank(message = "微信昵称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nickName;

    /**
     * 微信性别
     */
    @NotBlank(message = "微信性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private Integer sex;

    /**
     * 微信城市
     */
    @NotBlank(message = "微信城市不能为空", groups = { AddGroup.class, EditGroup.class })
    private String city;

    /**
     * 微信省份
     */
    @NotBlank(message = "微信省份不能为空", groups = { AddGroup.class, EditGroup.class })
    private String province;

    /**
     * 微信国家
     */
    @NotBlank(message = "微信国家不能为空", groups = { AddGroup.class, EditGroup.class })
    private String country;

    /**
     * 微信头像
     */
    @NotBlank(message = "微信头像不能为空", groups = { AddGroup.class, EditGroup.class })
    private String headImgUrl;

    /**
     * unionid
     */
    @NotBlank(message = "unionid不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unionId;


}
