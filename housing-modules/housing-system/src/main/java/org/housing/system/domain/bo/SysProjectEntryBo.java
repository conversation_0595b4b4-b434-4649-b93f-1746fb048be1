package org.housing.system.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.housing.common.core.validate.AddGroup;
import org.housing.common.core.validate.EditGroup;
import org.housing.system.domain.SysProjectEntry;

/**
 * 项目报名业务对象 sys_notice_entry
 *
 * <AUTHOR>
 * @date 2024-09-29
 */
@Data
@AutoMapper(target = SysProjectEntry.class, reverseConvertGenerate = false)
public class SysProjectEntryBo {

    /**
     * 项目报名主键
     */
    @NotNull(message = "项目报名主键不能为空", groups = { EditGroup.class })
    private Long projectEntryId;

    /**
     * 项目主键
     */
    @NotNull(message = "项目不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long projectId;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String companyName;

    /**
     * 信用代码
     */
    @NotBlank(message = "信用代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String creditCode;

    /**
     * 联系人
     */
    @NotBlank(message = "联系人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String manageUser;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phoneNumber;

    /**
     * 邮箱
     */
    @NotBlank(message = "联系邮箱不能为空", groups = { AddGroup.class, EditGroup.class })
    private String email;

    /**
     * 营业执照
     */
    @NotBlank(message = "营业执照不能为空", groups = { AddGroup.class, EditGroup.class })
    private String license;

    /**
     * 法人授权委托书
     */
    @NotBlank(message = "法人授权委托书不能为空", groups = { AddGroup.class, EditGroup.class })
    private String powerBook;

    /**
     * 其他材料
     */
    @NotBlank(message = "其他材料不能为空", groups = { AddGroup.class, EditGroup.class })
    private String other;

    /**
     * 支付状态 服务端用
     */
    private String payStatus;

    /**
     * 审核状态 服务端用
     */
    private String audits;

    /**
     * 报名人员主键 服务端用
     */
    private Long entryUserId;

    /**
     * 租户主键 服务端用
     */
    private String tenantId;

    /**
     * 短信验证码
     */
    @NotBlank(message = "短信验证码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String smsCode;

    /**
     * 身份证正面
     */
    @NotBlank(message = "身份证正面不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCardZ;

    /**
     * 身份证反面
     */
    @NotBlank(message = "身份证反面不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCardF;
}
