/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CheckSystemButton: typeof import('./src/components/button/checkSystemButton.vue')['default']
    FullPreview: typeof import('./src/components/fullPreview/index.vue')['default']
    'Icon.h5': typeof import('./src/components/alIconfont/icon.h5.vue')['default']
    'Icon.weapp': typeof import('./src/components/alIconfont/icon.weapp.vue')['default']
    MineButton: typeof import('./src/components/SideBar/mine-button.vue')['default']
    MyToast: typeof import('./src/components/myToast/index.vue')['default']
    NutAnimate: typeof import('@nutui/nutui-taro')['Animate']
    NutButton: typeof import('@nutui/nutui-taro')['Button']
    NutCell: typeof import('@nutui/nutui-taro')['Cell']
    NutEmpty: typeof import('@nutui/nutui-taro')['Empty']
    NutForm: typeof import('@nutui/nutui-taro')['Form']
    NutFormItem: typeof import('@nutui/nutui-taro')['FormItem']
    NutInput: typeof import('@nutui/nutui-taro')['Input']
    NutInputNumber: typeof import('@nutui/nutui-taro')['InputNumber']
    NutPopup: typeof import('@nutui/nutui-taro')['Popup']
    NutSearchbar: typeof import('@nutui/nutui-taro')['Searchbar']
    NutSwiper: typeof import('@nutui/nutui-taro')['Swiper']
    NutSwiperItem: typeof import('@nutui/nutui-taro')['SwiperItem']
    NutToast: typeof import('@nutui/nutui-taro')['Toast']
    NutUploader: typeof import('@nutui/nutui-taro')['Uploader']
    SearchChoosePop: typeof import('./src/components/searchChoosePop/index.vue')['default']
    SideBar: typeof import('./src/components/SideBar/index.vue')['default']
    UpdatePop: typeof import('./src/components/pop/updatePop/index.vue')['default']
  }
}
