<template>
  <scroll-view :class="styles.myContainer" class="pageIn" scroll-y="true">
    <navbar title="问卷详情" background-color="#f5f5f9" />

    <view v-if="data.loading" class="loading-container">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>

    <view v-else-if="data.question" class="question-detail">
      <!-- 问卷基本信息 -->
      <view class="question-header">
        <view class="question-title">{{ data.question.questionTitle }}</view>

        <view class="question-description" v-if="data.question.questionRemark">
          {{ data.question.questionRemark }}
        </view>

        <view class="question-info">
          <view class="info-item">
            <text class="info-label">问卷时间范围：</text>
            <text class="info-value">{{ formatTime(data.question.questionStartTime) }} 至 {{ formatTime(data.question.questionEndTime) }}</text>
          </view>
        </view>
      </view>

      <!-- 问卷题目 -->
      <view class="question-items">
        <view
          v-for="(item, index) in data.question.questionItems"
          :key="item.questionItemsId"
          class="question-item-card"
        >
          <view class="item-header">
            <view class="item-title">{{ String(index + 1).padStart(2, '0') }} {{ item.content }}</view>
          </view>

          <!-- 选择题：根据itemType判断单选(1)还是多选(3) -->
          <view v-if="item.itemType === 1 || item.itemType === 3" class="item-options">
            <!-- 单选题 -->
            <radio-group v-if="item.itemType === 1" @change="handleRadioChange(item.questionItemsId, $event)">
              <view
                v-for="option in data.question.questionOptions"
                :key="option.optionsId"
                class="option-item"
                :class="{ selected: isOptionSelected(item, option.optionsId) }"
              >
                <radio
                  :value="option.optionsId.toString()"
                  :checked="isOptionSelected(item, option.optionsId)"
                  class="option-radio"
                />
                <view class="option-content">
                  <view class="option-text">{{ option.optionContent }}</view>
                </view>
              </view>
            </radio-group>

            <!-- 多选题 -->
            <checkbox-group v-else-if="item.itemType === 3" @change="handleCheckboxChange(item.questionItemsId, $event)">
              <view
                v-for="option in data.question.questionOptions"
                :key="option.optionsId"
                class="option-item"
                :class="{ selected: isOptionChecked(item, option.optionsId) }"
              >
                <checkbox
                  :value="option.optionsId.toString()"
                  :checked="isOptionChecked(item, option.optionsId)"
                  class="option-checkbox"
                />
                <view class="option-content">
                  <view class="option-text">{{ option.optionContent }}</view>
                </view>
              </view>
            </checkbox-group>
          </view>

          <!-- 填空题 -->
          <view v-else-if="item.itemType === 2" class="item-input">
            <textarea
              :value="data.answers[item.questionItemsId] || ''"
              @input="(e) => data.answers[item.questionItemsId] = e.detail.value"
              placeholder="请输入您的建议..."
              class="answer-textarea"
              :maxlength="500"
            />
          </view>
        </view>
      </view>

      <!-- 签名提示区域 -->
      <view v-if="data.question.signature === 1" class="signature-notice">
        <view class="signature-notice-content" @tap="handleSignatureClick">
          <view class="notice-icon">✍️</view>
          <view class="notice-text">
            <view class="notice-title">电子签名确认</view>
            <view class="notice-desc">本问卷需要您的电子签名确认</view>
          </view>
          <view class="signature-status">
            <view v-if="data.signatureImageUrl" class="signed-status">
              <text class="status-icon">✅</text>
              <text class="status-text">已签名</text>
            </view>
            <view v-else class="unsigned-status">
              <text class="status-icon">⚠️</text>
              <text class="status-text">点击签名</text>
            </view>
          </view>
        </view>

        <!-- 已签名时显示签名图片 -->
        <view v-if="data.signatureImageUrl" class="signature-preview">
          <view class="preview-label">您的签名</view>
          <image :src="data.signatureImageUrl" class="signature-img" mode="aspectFit" />
          <button @tap="handleReSign" class="re-sign-btn">重新签名</button>
        </view>
      </view>


      <!-- 提交按钮 -->
      <view class="submit-section">
        <button
          @tap="submitAnswers"
          class="submit-btn"
          :class="{ disabled: data.submitting || !canSubmit }"
          :disabled="data.submitting || !canSubmit"
        >
          <text>{{ data.submitting ? '提交中...' : '提交' }}</text>
        </button>
      </view>
    </view>

    <!-- 错误状态 -->
    <view v-else class="error-state">
      <view class="error-icon">😕</view>
      <view class="error-text">加载失败，请重试</view>
      <button @tap="loadQuestionDetail" class="retry-btn">重新加载</button>
    </view>

    <!-- toast提示 -->
    <my-toast-components ref="myToast" :duration="2500" />

    <!-- 签名弹窗 -->
    <view v-show="data.question?.signature === 1 && showSignatureModal" class="signature-modal">
      <view class="modal-mask" @tap="hideSignatureModal"></view>
      <view class="signature-popup">
        <view class="popup-header">
          <text class="popup-title">本次投票需要您的输入信息身份进行核对</text>
          <view class="close-btn" @tap="hideSignatureModal">✕</view>
        </view>

        <view class="signature-content">
          <view class="signature-label">签名</view>
          <view class="signature-canvas-container">
            <canvas
              canvas-id="signatureCanvas"
              class="signature-canvas"
              @touchstart="handleTouchStart"
              @touchmove="handleTouchMove"
              @touchend="handleTouchEnd"
            />
            <view v-if="!data.signatureImageUrl && !hasSignatureContent" class="signature-placeholder">
              点击去签名 >
            </view>
          </view>

          <view class="signature-buttons">
            <button @tap="clearSignature" class="clear-btn">
              清除重写
            </button>
            <button @tap="saveSignature" class="confirm-btn">
              确认签名
            </button>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>
</template>

<script lang="ts" setup>
import { Navbar } from '@fishui/taro-vue';
// @ts-ignore
import styles from './styles.scss';
import { reactive, ref, onMounted, computed } from 'vue';
import myToastComponents from '@/components/myToast/index.vue';
import { getQuestionDetail, answerAdd } from '@/apis/question';
import type {
    IOwnerAnswerRequest,
    IQuestion
} from "@/apis/question/model";
import Taro, { useRouter, getCurrentInstance } from '@tarojs/taro';
import type { BaseEventOrig, TextareaProps } from '@tarojs/components';
import dayjs from 'dayjs';

definePageConfig({ backgroundColor: '#f5f5f9' });

const router = useRouter();
const myToast = ref<any>();

const data = reactive({
  question: null as IQuestion | null,
  loading: true,
  submitting: false,
  answers: {} as Record<number, string>, // 题目ID -> 答案的映射
  checkboxAnswers: {} as Record<number, string[]>, // 多选题答案：题目ID -> 选项ID数组
  signatureImageUrl: '', // 签名图片URL
  signatureOssId: '', // 签名图片在OSS中的ID，用于提交给服务器
});

// 显示签名弹窗
const showSignatureModal = ref(false);

// 处理签名区域点击
const handleSignatureClick = () => {
  if (!data.signatureImageUrl) {
    // 如果还没有签名，打开签名弹窗
    displaySignatureModal();
  }
};

// 处理重新签名
const handleReSign = () => {
  // 清除现有签名并打开签名弹窗
  data.signatureImageUrl = '';
  data.signatureOssId = '';
  hasSignatureContent.value = false;
  displaySignatureModal();
};

// 显示签名弹窗
const displaySignatureModal = () => {
  showSignatureModal.value = true;
  // 延迟初始化画布，确保DOM已渲染
  setTimeout(() => {
    initCanvas();
  }, 100);
};

// 隐藏签名弹窗
const hideSignatureModal = () => {
  showSignatureModal.value = false;
};

// 画布相关
let canvasContext: any = null;
let isDrawing = false;
let lastX = 0;
let lastY = 0;
let hasSignatureContent = ref(false);

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm');
};

// 判断选项是否被选中（根据userAnswer匹配）
const isOptionSelected = (item: any, optionId: string | number) => {
  // 优先使用当前答案状态，如果没有则使用userAnswer
  const currentAnswer = data.answers[item.questionItemsId];
  if (currentAnswer !== undefined && currentAnswer !== null) {
    return currentAnswer === optionId.toString();
  }
  // 如果有userAnswer，使用userAnswer判断
  if (item.userAnswer) {
    return item.userAnswer === optionId.toString();
  }
  return false;
};

// 判断多选项是否被选中
const isOptionChecked = (item: any, optionId: string | number) => {
  // 优先使用当前多选答案状态
  const selectedOptions = data.checkboxAnswers[item.questionItemsId];
  if (selectedOptions && selectedOptions.length > 0) {
    return selectedOptions.includes(optionId.toString());
  }
  // 如果有userAnswer，解析多选答案（用逗号分隔）
  if (item.userAnswer) {
    const userSelectedOptions = item.userAnswer.split(',');
    return userSelectedOptions.includes(optionId.toString());
  }
  return false;
};

// 检查是否可以提交（使用计算属性避免频繁重新计算导致渲染问题）
const canSubmit = computed(() => {
  if (!data.question?.questionItems) return false;

  console.log('=== 当前状态检查 ===');
  console.log('data.answers:', data.answers);
  console.log('data.question.questionItems:', data.question.questionItems?.map(item => ({
    id: item.questionItemsId,
    type: item.itemType
  })));

  // 检查每个题目是否都有有效答案
  const validAnswers = data.question.questionItems.filter(item => {
    const answer = data.answers[item.questionItemsId];
    const isValid = answer !== undefined && answer !== null && String(answer).trim() !== '';
    console.log(`题目ID: ${item.questionItemsId}, 答案: "${answer}", 类型: ${typeof answer}, 有效: ${isValid}`);
    return isValid;
  });

  const answersComplete = validAnswers.length === data.question.questionItems.length;
  console.log(`有效答案: ${validAnswers.length}/${data.question.questionItems.length}, 答案完整: ${answersComplete}`);

  // 检查签名状态（如果问卷需要签名）
  let signatureComplete = true;
  if (data.question.signature === 1) {
    signatureComplete = !!data.signatureOssId;
    console.log(`问卷需要签名, signatureOssId: "${data.signatureOssId}", 签名完整: ${signatureComplete}`);
  } else {
    console.log('问卷不需要签名');
  }

  const result = answersComplete && signatureComplete;
  console.log(`最终结果: 答案完整(${answersComplete}) && 签名完整(${signatureComplete}) = ${result}`);

  return result;
});

// 处理单选变化
const handleRadioChange = (itemId: string | number, event: any) => {
  const value = event.detail.value;
  data.answers[itemId] = value; // 直接使用原始ID作为键
};

// 处理多选变化
const handleCheckboxChange = (itemId: string | number, event: any) => {
  const selectedValues = event.detail.value;
  data.checkboxAnswers[itemId] = selectedValues; // 直接使用原始ID作为键
  // 同时将多选答案用逗号拼接存储到answers中
  data.answers[itemId] = selectedValues.join(',');
};

// 初始化画布
const initCanvas = () => {
  canvasContext = Taro.createCanvasContext('signatureCanvas');
  canvasContext.setStrokeStyle('#000000');
  canvasContext.setLineWidth(2);
  canvasContext.setLineCap('round');
  canvasContext.setLineJoin('round');
};

// 触摸开始
const handleTouchStart = (event: any) => {
  isDrawing = true;
  hasSignatureContent.value = true;
  const touch = event.touches[0];
  lastX = touch.x;
  lastY = touch.y;

  canvasContext.beginPath();
  canvasContext.moveTo(lastX, lastY);
};

// 触摸移动
const handleTouchMove = (event: any) => {
  if (!isDrawing) return;

  const touch = event.touches[0];
  const currentX = touch.x;
  const currentY = touch.y;

  canvasContext.lineTo(currentX, currentY);
  canvasContext.stroke();
  canvasContext.draw(true);

  lastX = currentX;
  lastY = currentY;
};

// 触摸结束
const handleTouchEnd = () => {
  isDrawing = false;
};

// 清除签名
const clearSignature = () => {
  canvasContext.clearRect(0, 0, 300, 150);
  canvasContext.draw();
  data.signatureImageUrl = '';
  data.signatureOssId = '';
  hasSignatureContent.value = false;
};

// 保存签名
const saveSignature = () => {
  if (!hasSignatureContent.value) {
    myToast.value?.myToastShow({
      msg: '请先进行签名',
      icon: 'failure'
    });
    return;
  }

  Taro.canvasToTempFilePath({
    canvasId: 'signatureCanvas',
    success: (res) => {
      // 上传签名图片
      uploadSignature(res.tempFilePath);
    },
    fail: (err) => {
      console.error('保存签名失败:', err);
      myToast.value?.myToastShow({
        msg: '保存签名失败',
        icon: 'failure'
      });
    }
  });
};

// 上传签名图片
const uploadSignature = async (filePath: string) => {
  try {
    // 参考注册页面，使用完整的硬编码URL
    const uploadUrl = process.env.BUILD_ENV === 'prod'
      ? 'https://housing-admin.ninthone.cn/prod-api/resource/oss/upload'
      : 'http://127.0.0.1:8080/resource/oss/upload';

    Taro.uploadFile({
      url: uploadUrl,
      filePath: filePath,
      name: 'file',
      header: {
        // 可能需要添加认证头
        'Authorization': 'Bearer ' + Taro.getStorageSync('token')
      },
      success: (res) => {
        const result = JSON.parse(res.data);
        if (result.code === 200) {
          // 参考注册页面处理方式，获取SysOssVo结构中的ossId和url
          const ossData = result.data; // SysOssVo对象
          data.signatureImageUrl = ossData.url || ossData.ossId; // 优先使用url，fallback到ossId
          data.signatureOssId = String(ossData.ossId); // 保存ossId用于提交给服务器，确保字符串格式

          // 签名成功后关闭弹窗
          hideSignatureModal();

          myToast.value?.myToastShow({
            msg: '签名保存成功',
            icon: 'success'
          });
        } else {
          throw new Error(result.msg || '上传失败');
        }
      },
      fail: (err) => {
        console.error('上传签名失败:', err);
        myToast.value?.myToastShow({
          msg: '上传签名失败',
          icon: 'failure'
        });
      }
    });
  } catch (error) {
    console.error('上传签名失败:', error);
    myToast.value?.myToastShow({
      msg: '上传签名失败',
      icon: 'failure'
    });
  }
};

// 提交答案
const submitAnswers = async () => {
  // 验证必填项
  if (!data.question) return;

  const answeredCount = Object.keys(data.answers).length;
  if (answeredCount !== data.question.questionItems.length) {
    myToast.value?.myToastShow({
      msg: '请完成所有题目',
      icon: 'failure'
    });
    return;
  }

  // 如果需要签名且还未签名，显示签名弹窗
  if (data.question.signature === 1 && !data.signatureOssId) {
    displaySignatureModal();
    return;
  }

  // 如果在签名弹窗中，需要先保存签名
  if (showSignatureModal.value && hasSignatureContent.value && !data.signatureImageUrl) {
    await saveSignature();
    if (!data.signatureImageUrl) {
      myToast.value?.myToastShow({
        msg: '请先保存签名',
        icon: 'failure'
      });
      return;
    }
  }

  try {
    data.submitting = true;

    const requestData: IOwnerAnswerRequest = {
      questionId: data.question.questionId, // 直接使用字符串类型
      answers: data.answers
    };

    // 如果有签名，添加签名字段
    if (data.signatureOssId) {
      requestData.eSignImg = data.signatureOssId; // 传递OSS中的签名图片ID，保持字符串格式避免精度丢失
    }

    await answerAdd(requestData);

    // 隐藏签名弹窗
    hideSignatureModal();

    myToast.value?.myToastShow({
      msg: '提交成功',
      icon: 'success'
    });

    // 延迟返回上一页
    setTimeout(() => {
      Taro.navigateBack();
    }, 1500);

  } catch (error) {
    console.error('提交答案失败:', error);
    myToast.value?.myToastShow({
      msg: '提交失败，请重试',
      icon: 'failure'
    });
  } finally {
    data.submitting = false;
  }
};

// 加载问卷详情
const loadQuestionDetail = async () => {
  // 尝试多种方式获取questionId参数（保持字符串格式避免精度丢失）
  let questionId: string = '';

  // 方式1: useRouter
  if (router.params?.questionId) {
    questionId = router.params.questionId;
  }

  // 方式2: getCurrentInstance
  if (!questionId) {
    const instance = getCurrentInstance();
    if (instance?.router?.params?.questionId) {
      questionId = instance.router.params.questionId;
    }
  }

  // 方式3: Taro.getCurrentInstance (兼容旧版本)
  if (!questionId) {
    const currentInstance = Taro.getCurrentInstance();
    if (currentInstance?.router?.params?.questionId) {
      questionId = currentInstance.router.params.questionId;
    }
  }

  console.log('获取到的questionId (字符串):', questionId);
  console.log('router.params:', router.params);
  console.log('getCurrentInstance params:', getCurrentInstance()?.router?.params);
  console.log('Taro.getCurrentInstance params:', Taro.getCurrentInstance()?.router?.params);

  if (!questionId) {
    myToast.value?.myToastShow({
      msg: `问卷ID无效: ${questionId}`,
      icon: 'failure'
    });
    return;
  }

  try {
    data.loading = true;
    console.log('开始请求问卷详情，questionId:', questionId);
    const res = await getQuestionDetail(questionId);
    console.log('问卷详情响应:', res);

    if (!res) {
      throw new Error('未获取到问卷数据');
    }

    data.question = res;

    // 初始化答案状态：如果有userAnswer，设置到answers中
    if (res.questionItems) {
      res.questionItems.forEach(item => {
        // 只为有答案的题目设置初始值，没有答案的不设置空字符串
        if (item.userAnswer) {
          data.answers[item.questionItemsId] = item.userAnswer;

          // 如果是多选题，还需要初始化checkboxAnswers
          if (item.itemType === 3) {
            data.checkboxAnswers[item.questionItemsId] = item.userAnswer.split(',');
          }
        } else if (item.itemType === 3) {
          // 多选题即使没有答案也要初始化为空数组
          data.checkboxAnswers[item.questionItemsId] = [];
        }
      });
    }
  } catch (error) {
    console.error('获取问卷详情失败:', error);
    myToast.value?.myToastShow({
      msg: `获取问卷详情失败: ${error.message || error}`,
      icon: 'failure'
    });
  } finally {
    data.loading = false;
  }
};

onMounted(() => {
  loadQuestionDetail();
});
</script>
