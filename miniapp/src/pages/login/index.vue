<template>
  <view :class="styles.myContainer">
    <navbar title="登录" hide-back background-color="transparent" />

    <!-- logo -->
    <view class="head">
      <image src="@/assets/images/lpt/logo.jpg" />
    </view>

    <!-- 页面文案 -->
    <view class="tip">
      <view class="t1"> 业主投票小助手是帮助业主提供投票选择的工具。 </view>
      <view class="t2"> 自主选择，快速投票! </view>
    </view>

    <!-- 绑定信息 -->
    <view class="info">
      <view class="t3"> 本助手需要登录使用，输入下方信息立即登录 </view>

      <nut-form>
        <nut-form-item label="小区">
          <view
            class="content"
            @tap="
              () => {
                data.sheetShow = true;
              }
            "
          >
            {{ data.xqVal.housingName || '点击选择小区' }}
          </view>
        </nut-form-item>
        <nut-form-item label="业主姓名">
          <nut-input v-model="data.ownerName" class="content" placeholder="请输入业主姓名" type="text" />
        </nut-form-item>
        <nut-form-item label="手机号">
          <nut-input v-model="data.ownerPhone" class="content" placeholder="请输入手机号" type="text" />
        </nut-form-item>
        <nut-form-item label="验证码">
          <view class="smstConent">
            <nut-input v-model="data.smsCode" placeholder="请输入验证码" class="content" type="text" />
            <nut-button
              :style="{
                height: '26px',
              }"
              class="smsbtn"
              type="info"
              :disabled="data.canSendTime > 0"
              @click="sendSms"
            >
              {{ data.canSendTime > 0 ? `重发${data.canSendTime}秒` : '获取验证码' }}
            </nut-button>
          </view>        </nut-form-item>
      </nut-form>

      <view class="btns">
        <nut-button class="sqBtn" @click="handleClickBtnLogin"> 登录 </nut-button>
        <nut-button class="sqBtn" @click="handleClickBtnRegister"> 去注册 </nut-button>
      </view>
    </view>
    <!-- 选择小区  -->
    <searchChoosePop
      v-model:popChooseVisible="data.sheetShow"
      title="选择小区"
      searchPlaceholder="搜索小区名称"
      :choose-list="[data.xqVal]"
      :range="data.xqList"
      range-name="housingName"
      range-value="housingId"
      @choose-confirm="handleConfimPick"
    />
  </view>
  <!-- toast提示 -->
  <my-toast-components ref="myToast" :duration="2500" />
</template>
<script lang="ts" setup>
import {
    Navbar
} from '@fishui/taro-vue';
// @ts-ignore
import styles
    from './styles.scss';
import {
    reactive,
    ref
} from 'vue';
import myToastComponents
    from '@/components/myToast/index.vue';
import Taro, {
    useDidShow
} from '@tarojs/taro';
import {
    getHousingList,
    getSmsCode,
    login
} from '@/apis/login';
import requestInstance, {
    tokenUtil
} from '@/utils/request';
import searchChoosePop
    from '@/components/searchChoosePop/index.vue';

definePageConfig({ backgroundColor: '#f3f3fe' });

const myToast = ref<any>();

const data = reactive({
  xqList: [],
  sheetShow: false,
  xqVal: {} as any,
  ownerName: '',
  ownerPhone: '',
  smsCode: '',
  showRegister: true,
  canSendTime: 0,
  intervalTimer: null as any,

});

const sendSms = async () => {
    if (data.ownerPhone) {
      data.canSendTime = 60;
      data.intervalTimer = setInterval(() => {
        if (data.canSendTime > 0) {
          data.canSendTime--;
        } else {
          clearInterval(data.intervalTimer!);
        }
      }, 1000);
      await getSmsCode(data.ownerPhone);
    }else{
      myToast.value.myToastShow({
      icon: 'error',
      title: '请输入正确的手机号',
      duration: 2000,
    });
    }
  };

const handleClickBtnLogin = async () => {
  const wxCodeRes = await Taro.login();
  const res = await login({
    housingId: data.xqVal.housingId,
    ownerName: data.ownerName,
    ownerPhone: data.ownerPhone,
    smsCode: data.smsCode,
    jsCode: wxCodeRes.code,
  });

  tokenUtil.set(res.access_token);
  requestInstance.setConfig({ header: { Authorization: `Bearer ${res.access_token}` } });
  data.xqList = [];
  data.sheetShow = false;
  data.xqVal = {};
  data.ownerName = '';
  data.ownerPhone = '';
  data.showRegister = true;
  Taro.switchTab({ url: '/pages/menu/index' });
};

const handleClickBtnRegister = () => {
  Taro.navigateTo({ url: '/pages/register/index' });
};

// 获取小区列表
const getXQList = async () => {
  data.xqList = await getHousingList();
};

const handleConfimPick = (chooseList) => {
  data.xqVal =chooseList[0];
  data.sheetShow = false;
};

useDidShow(() => {
  getXQList();
});
</script>
