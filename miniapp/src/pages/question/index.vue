<template>
  <scroll-view :class="styles.myContainer" class="pageIn" scroll-y="true">
    <navbar title="问卷调查" background-color="#f5f5f9" />

    <view class="question-list">
      <view
        v-for="item in data.list"
        :key="item.questionId"
        class="question-item"
        :class="{ disabled: !isQuestionActive(item) || item.isVoted === 1 }"
        @tap="toQuestionDetail(item)"
      >
        <view class="question-header">
          <view class="question-title">{{ item.questionTitle }}</view>
          <view class="vote-status" :class="item.isVoted === 1 ? 'voted' : 'not-voted'">
            {{ item.isVoted === 1 ? '已参与' : '未参与' }}
          </view>
        </view>

        <view class="question-description">
          {{ item.questionRemark }}
        </view>

        <view class="question-time">
          <view class="time-title">问卷时间范围</view>
          <view class="time-range">
            {{ formatTime(item.questionStartTime) }} 至 {{ formatTime(item.questionEndTime) }}
          </view>
        </view>

        <view class="question-footer">
          <view class="question-action" :class="getActionClass(item)">
            {{ getActionText(item) }}
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-if="data.list.length === 0 && !data.loading" class="empty-state">
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无问卷调查</view>
      </view>

      <!-- 加载状态 -->
      <view v-if="data.loading" class="loading-state">
        <view class="loading-text">加载中...</view>
      </view>
    </view>

    <!-- toast提示 -->
    <my-toast-components ref="myToast" :duration="2500" />
  </scroll-view>
</template>

<script lang="ts" setup>
import {
    Navbar
} from '@fishui/taro-vue';
// @ts-ignore
import styles from './styles.scss';
import {
    reactive,
    ref
} from 'vue';
import myToastComponents
    from '@/components/myToast/index.vue';
import {
    getQuestions
} from '@/apis/question';
import type {
    IQuestion
} from '@/apis/question/model';
import Taro, {
    useDidShow
} from '@tarojs/taro';
import dayjs
    from 'dayjs';

definePageConfig({ backgroundColor: '#f5f5f9' });

const myToast = ref<any>();

const data = reactive({
  list: [] as IQuestion[],
  loading: false,
});

// 格式化时间
const formatTime = (timeStr: string) => {
  if (!timeStr) return '';
  return dayjs(timeStr).format('YYYY-MM-DD HH:mm');
};

// 判断问卷是否在有效期内
const isQuestionActive = (item: IQuestion) => {
  const now = dayjs();
  const startTime = dayjs(item.questionStartTime);
  const endTime = dayjs(item.questionEndTime);
  return now.isAfter(startTime) && now.isBefore(endTime);
};

// 获取操作文本
const getActionText = (item: IQuestion) => {
  if (!isQuestionActive(item)) {
    return '已过期';
  }
  return item.isVoted === 1 ? '已参与' : '点击参与';
};

// 获取操作样式类
const getActionClass = (item: IQuestion) => {
  if (!isQuestionActive(item)) {
    return 'expired';
  }
  return item.isVoted === 1 ? 'participated' : 'active';
};

// 跳转到问卷详情
const toQuestionDetail = (item: IQuestion) => {
  // 检查问卷是否已过期
  if (!isQuestionActive(item)) {
    myToast.value?.myToastShow({
      msg: '问卷调查已过期',
      icon: 'failure'
    });
    return;
  }

  // 检查是否已参与
  if (item.isVoted === 1) {
    myToast.value?.myToastShow({
      msg: '您已参与过此问卷',
      icon: 'failure'
    });
    return;
  }

  console.log('点击的问卷项:', item);
  console.log('问卷ID:', item.questionId);

  // 跳转到问卷详情页面
  Taro.navigateTo({
    url: `/pages/questionDetail/index?questionId=${item.questionId}`
  });
};

// 获取问卷列表
const getQuestionList = async () => {
  try {
    data.loading = true;
    const res = await getQuestions({
      current: 1,
      pageSize: 20
    });

    if (res && res.rows) {
      data.list = res.rows;
    } else if (Array.isArray(res)) {
      // 如果直接返回数组
      data.list = res;
    }
  } catch (error) {
    console.error('获取问卷列表失败:', error);
    myToast.value?.myToastShow({
      msg: '获取问卷列表失败',
      icon: 'failure'
    });
  } finally {
    data.loading = false;
  }
};

// 页面显示时刷新数据
useDidShow(() => {
  getQuestionList();
});

// 初始化加载
getQuestionList();
</script>
