page {
  background: #f5f5f9;
}

.myContainer {
  width: 100vw;
  height: 100vh;

  :global {
    .question-list {
      padding: 15px;

      .question-item {
        background: #ffffff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
        transition: all 0.2s ease;
        min-height: 120px;

        &:active {
          transform: scale(0.99);
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
        }

        // 已过期或已参与的问卷样式
        &.disabled {
          opacity: 0.6;
          cursor: not-allowed;

          &:active {
            transform: none;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
          }
        }

        .question-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 10px;

          .question-title {
            flex: 1;
            font-size: 16px;
            font-weight: 600;
            color: #333333;
            line-height: 1.3;
            margin-right: 12px;
            word-break: break-word;
            overflow-wrap: break-word;
          }

          .vote-status {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            white-space: nowrap;
            flex-shrink: 0;
            align-self: flex-start;

            &.voted {
              background: #e8f5e8;
              color: #52c41a;
            }

            &.not-voted {
              background: #fff2e8;
              color: #fa8c16;
            }

            &.expired {
              background: #f5f5f5;
              color: #999999;
            }
          }
        }

        .question-description {
          font-size: 14px;
          color: #333333;
          line-height: 1.4;
          margin-bottom: 12px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-word;
          max-height: 39.2px; /* 14px * 1.4 * 2 = 39.2px */
        }

        .question-time {
          margin-bottom: 12px;

          .time-title {
            font-size: 12px;
            color: #333333;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .time-range {
            font-size: 12px;
            color: #333333;
            font-weight: 400;
            line-height: 1.4;
          }
        }

        .question-footer {
          display: flex;
          justify-content: center;
          align-items: center;
          padding-top: 10px;
          border-top: 1px solid #f0f0f0;

          .question-action {
            font-size: 14px;
            font-weight: 500;

            &.active {
              color: #333333;
            }

            &.participated {
              color: #52c41a;
            }

            &.expired {
              color: #999999;
            }
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
        opacity: 0.6;
      }

      .empty-text {
        font-size: 14px;
        color: #999999;
      }
    }

    .loading-state {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 30px 20px;

      .loading-text {
        font-size: 14px;
        color: #999999;
      }
    }
  }
}
