.fullPreview {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background-color: rgba(129, 118, 254, .3);
  opacity: 1;


  :global {
    .lptBg {
      z-index: -1;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
    .title {
      width: 100%;
      text-align: center;
      position: absolute;
      top: 30%;
      // transform: translate(0, -30%);
      font-size: 28px;
      font-weight: 600;
      font-family: monospace;
      color: #222;
    }

    .logo {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-60%, -30%);
      width: 120px;
      height: 120px;

      margin: 10px;
      // border: 1px  #333 ;
      border-style: dashed;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      z-index: 1;
      overflow: hidden;

      .bgImg {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0px;
        opacity: .9;
      }

    }

    .hypnotic-5:local {
      width: 230px;
      height: 230px;
      display: grid;
      z-index: -1;
      background:
        linear-gradient(to bottom left, #0000 calc(50% - 1px), currentColor 0 calc(50% + 1px), #333 0) right/50% 100%,
        linear-gradient(to bottom right, #0000 calc(50% - 1px), currentColor 0 calc(50% + 1px), #333 0) left /50% 100%,
        linear-gradient(currentColor 0 0) bottom/100% 2px;
      background-repeat: no-repeat;
      transform-origin: 50% 66%;
      animation: h5 3s infinite linear;
    }

    .hypnotic-5::before:local,
    .hypnotic-5::after:local {
      content: "";
      grid-area: 1/1;
      background: inherit;
      transform-origin: inherit;
      animation: inherit;
    }

    .hypnotic-5::after:local {
      animation-duration: 1.5s;
    }

    @keyframes h5 {
      100% {
        transform: rotate(1turn)
      }
    }
    @keyframes page {
      100% {
        opacity: 0;
      }
    }
  }
}
.dispoint{
  animation: page 1.7s ease-in-out;
  animation-delay: 1.2s;
  animation-fill-mode : forwards;
}
