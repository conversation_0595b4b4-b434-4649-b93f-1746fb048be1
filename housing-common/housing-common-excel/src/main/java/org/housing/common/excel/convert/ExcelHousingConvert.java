package org.housing.common.excel.convert;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;
import org.housing.common.core.service.HousingService;
import org.housing.common.core.utils.SpringUtils;

/**
 * 小区名称-id转换处理
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelHousingConvert implements Converter<Object> {

    @Override
    public Class<Object> supportJavaTypeKey() {
        return Object.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Object convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        String housingName = cellData.getStringValue();
        HousingService housingService = SpringUtils.getBean(HousingService.class);
        return Convert.convert(contentProperty.getField().getType(), housingService.selectHousingIdByName(housingName));
    }

    @Override
    public WriteCellData<String> convertToExcelData(Object object, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        if (ObjectUtil.isNull(object)) {
            return new WriteCellData<>("");
        }
        Long value = Convert.toLong(object);
        HousingService housingService = SpringUtils.getBean(HousingService.class);
        String housingName = housingService.selectHousingNameByIds(value);
        return new WriteCellData<>(housingName);
    }
}
