package org.housing.common.translation.core.impl;

import lombok.AllArgsConstructor;
import org.housing.common.core.service.UserService;
import org.housing.common.translation.annotation.TranslationType;
import org.housing.common.translation.constant.TransConstant;
import org.housing.common.translation.core.TranslationInterface;

/**
 * 手机号翻译实现
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.USER_ID_TO_PHONE)
public class PhoneTranslationImpl implements TranslationInterface<String> {

    private final UserService userService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof Long id) {
            return userService.selectPhonenumberById(id);
        }
        return null;
    }
}
