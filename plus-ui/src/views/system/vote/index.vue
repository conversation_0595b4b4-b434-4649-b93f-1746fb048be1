<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="投票标题" prop="voteTitle">
              <el-input v-model="queryParams.voteTitle" placeholder="请输入投票标题" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:topic:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:topic:edit']" type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:topic:edit']" type="success" plain icon="Download" :disabled="multiple" @click="handleStatistics()"
              >导出统计</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:topic:edit']" type="success" plain icon="Download" :disabled="multiple" @click="handleOwnerVoteInfo()"
              >导出业主投票详情</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:topic:edit']" type="success" plain icon="Document" :disabled="multiple" @click="handlePaperVote()"
              >纸质投票</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button v-hasPermi="['system:topic:remove']" type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()"
              >删除</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Close" @click="handleClose">关闭</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" :data="voteList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column v-if="false" label="投票主键" align="center" prop="voteId" />
        <el-table-column label="投票标题" align="center" prop="voteTitle" :show-overflow-tooltip="true" />
        <el-table-column label="投票数" align="center" :show-overflow-tooltip="true">
          <template #default="scope">
            <router-link
              :to="{
                path: `/system/vote/ownerVote/index/${scope.row.housingId}/${scope.row.topicId}/${scope.row.voteId}`,
                query: {
                  topicOptionsArray: route.query.topicOptionsArray // 将从 topic 传来的参数继续传递
                }
              }"
              class="link-type"
            >
              <span>{{ scope.row.voteCount }}</span>
            </router-link>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" prop="createByName" :show-overflow-tooltip="true" />
        <el-table-column label="更新人" align="center" prop="updateByName" :show-overflow-tooltip="true" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button v-hasPermi="['system:topic:edit']" link type="primary" icon="Edit" @click="handleUpdate(scope.row)"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-hasPermi="['system:topic:remove']" link type="primary" icon="Delete" @click="handleDelete(scope.row)"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" :total="total" @pagination="getList" />
    </el-card>
    <!-- 添加或修改投票对话框 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" append-to-body>
      <el-form ref="voteFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="投票标题" prop="voteTitle">
          <el-input v-model="form.voteTitle" placeholder="请输入投票标题" />
        </el-form-item>
        <el-form-item label="显示排序" prop="voteSort">
          <el-input-number v-model="form.voteSort" :min="1" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导出统计对话框 -->
    <el-dialog v-model="statisticsDialog.visible" :title="statisticsDialog.title" width="500px" append-to-body>
      <el-form ref="statisticsDialogFormRef" :model="statisticsDialogForm" :rules="statisticsDialogRules" label-width="80px">
        <el-form-item label="表单标题" prop="title">
          <el-input v-model="statisticsDialogForm.title" placeholder="请输入统计标题" />
        </el-form-item>
        <el-form-item label="表单主题" prop="theme">
          <el-input v-model="statisticsDialogForm.theme" placeholder="请输入统计主题" />
        </el-form-item>
        <el-form-item label="表单日期" prop="date">
          <el-input v-model="statisticsDialogForm.date" placeholder="请输入统计日期,年月日" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitStatisticsForm">确 定</el-button>
          <el-button @click="cancelStatistics">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 纸质投票对话框 -->
    <el-dialog v-model="paperVoteDialog.visible" :title="paperVoteDialog.title" width="800px" append-to-body>
      <el-form ref="paperVoteFormRef" :model="paperVoteForm" label-width="100px">
        <div class="paper-vote-container">
          <el-form-item label="业主主键" prop="ownerId">
            <el-input v-model="paperVoteForm.ownerId" placeholder="请输入业主主键" />
          </el-form-item>
          <div v-for="vote in paperVoteForm.voteList" :key="vote.voteId" class="vote-card">
            <div class="vote-title">{{ vote.voteTitle }}</div>
            <div class="vote-options">
              <el-radio-group v-model="vote.advice">
                <div class="options-row">
                  <div v-for="option in topicOptions" :key="option" class="vote-option">
                    <el-radio :value="String(option).split('-')[1]">
                      {{ String(option).split('-')[0] }}
                    </el-radio>
                  </div>
                  <div class="vote-option special-option">
                    <el-radio :value="-1">
                      已参与未表决
                    </el-radio>
                  </div>
                </div>
              </el-radio-group>
            </div>
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitPaperVote">提交投票</el-button>
          <el-button @click="cancelPaperVote">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Vote" lang="ts">
import {
  addVote,
  delVote,
  getVote,
  listVote,
  paperVoteInsert,
  updateVote
} from '@/api/system/vote';
import {
  PaperVoteForm,
  VoteForm,
  VoteQuery,
  VoteVO
} from '@/api/system/vote/types';
import {
  RouteLocationRaw
} from 'vue-router';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const voteList = ref<VoteVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const route = useRoute();

const queryFormRef = ref<ElFormInstance>();
const voteFormRef = ref<ElFormInstance>();
const statisticsDialogFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const statisticsDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const paperVoteDialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const topicOptions = ref<(string | number)[]>([]);

const initFormData: VoteForm = {
  voteId: undefined,
  housingId: undefined,
  topicId: undefined,
  voteTitle: undefined,
  voteSort: 1
};
const data = reactive<PageData<VoteForm, VoteQuery>>({
  form: { ...initFormData },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    topicId: undefined,
    voteTitle: undefined,
    params: {}
  },
  rules: {
    voteId: [{ required: true, message: '投票主键不能为空', trigger: 'blur' }],
    housingId: [{ required: true, message: '小区主键不能为空', trigger: 'change' }],
    topicId: [{ required: true, message: '议题主键不能为空', trigger: 'change' }],
    voteTitle: [{ required: true, message: '投票标题不能为空', trigger: 'blur' }],
    voteSort: [{ required: true, message: '投票排序不能为空', trigger: 'blur' }]
  }
});

// 导出统计数据
const statisticsDialogForm = reactive({
  voteIds: [] as (string | number)[],
  title: '',
  theme: '',
  date: '',
  housingId: undefined
});

// 导出统计验证规则
const statisticsDialogRules = {
  title: [{ required: true, message: '请输入统计标题', trigger: 'change' }],
  theme: [{ required: true, message: '请输入统计主题', trigger: 'change' }],
  date: [{ required: true, message: '请输入统计日期,年月日', trigger: 'blur' }]
};

const { queryParams, form, rules } = toRefs(data);

// 添加业主主键输入框的响应式变量
const ownerIdInput = ref('');

// 纸质投票表单的响应式数据
const paperVoteFormRef = ref<ElFormInstance>();
const paperVoteForm = reactive<PaperVoteForm>({
  ownerId: '',
  housingId: undefined,
  voteList: []
});

/** 查询投票列表 */
const getList = async () => {
  try {
    loading.value = true;
    const { topicId } = route.params;
    if (!topicId) {
      return;
    }
    queryParams.value.topicId = topicId as string;
    const res = await listVote(queryParams.value);
    voteList.value = res.rows;
    total.value = res.total;
  } catch (error) {
    console.error('Failed to get vote list:', error);
  } finally {
    loading.value = false;
  }
};

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
};

/** 表单重置 */
const reset = () => {
  form.value = { ...initFormData };
  voteFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: VoteVO[]) => {
  ids.value = selection.map((item) => item.voteId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = '添加投票';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: VoteVO) => {
  reset();
  const _voteId = row?.voteId || ids.value[0];
  const res = await getVote(_voteId);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = '修改投票';
};

/** 提交按钮 */
const submitForm = () => {
  form.value.housingId = route.params && (route.params.housingId as string);
  form.value.topicId = route.params && (route.params.topicId as string);
  voteFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.voteId) {
        await updateVote(form.value).finally(() => (buttonLoading.value = false));
      } else {
        await addVote(form.value).finally(() => (buttonLoading.value = false));
      }
      proxy?.$modal.msgSuccess('操作成功');
      dialog.visible = false;
      await getList();
    }
  });
};

/** 删除按钮操作 */
const handleDelete = async (row?: VoteVO) => {
  const _voteIds = row?.voteId || ids.value;
  await proxy?.$modal.confirm('是否确认删除投票编号为"' + _voteIds + '"的数据项？').finally(() => (loading.value = false));
  await delVote(_voteIds);
  proxy?.$modal.msgSuccess('删除成功');
  await getList();
};

/** 返回按钮操作 */
const handleClose = () => {
  const obj: RouteLocationRaw = {
    path: '/housing/topic'
  };
  proxy?.$tab.closeOpenPage(obj);
};

const handleStatistics = async (row?: VoteVO) => {
  if (row) {
    // 单行导出
    statisticsDialogForm.voteIds = [row.voteId];
  } else {
    // 多选导出
    statisticsDialogForm.voteIds = ids.value;
  }
  statisticsDialogForm.housingId = route.params && (route.params.housingId as string);
  statisticsDialog.visible = true;
  statisticsDialog.title = '导出统计';
};

// 提交导出统计
const submitStatisticsForm = () => {
  statisticsDialogFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      proxy
        ?.download(
          'system/vote/exportStatistics',
          {
            ...statisticsDialogForm
          },
          statisticsDialogForm.title + `.docx`
        )
        .then(() => {
          ElMessage.success('导出成功');
          statisticsDialog.visible = false;
        })
        .catch((error) => {
          console.error('Export error:', error);
          ElMessage.error('导出失败，请稍后重试');
        });
    }
  });
};

// 取消导出
const cancelStatistics = () => {
  statisticsDialog.visible = false;
  statisticsDialogFormRef.value?.resetFields();
};

const handleOwnerVoteInfo = async (row?: VoteVO) => {
  const voteIds = row?.voteId || ids.value;
  await proxy?.$modal.confirm('是否确认导出业主投票详情，编号为"' + voteIds + '"的数据项？').finally(() => (loading.value = false));
  proxy
    ?.download(
      'system/vote/exportOwnerVoteInfo',
      {
        voteIds
      },
      `业主投票详情.xlsx`
    )
    .then(() => {
      ElMessage.success('导出成功');
    })
    .catch((error) => {
      console.error('Export error:', error);
      ElMessage.error('导出失败，请稍后重试');
    });
};

/** 纸质投票按钮操作 */
const handlePaperVote = () => {
  const topicOptionsStr = route.query.topicOptionsArray;
  if (topicOptionsStr) {
    topicOptions.value = (Array.isArray(topicOptionsStr) ? topicOptionsStr : [topicOptionsStr]) as (string | number)[];
  } else {
    ElMessage.error('未获取到投票选项');
    return;
  }
  // 检查是否有投票数据
  if (!voteList.value.length) {
    ElMessage.error('未获取到投票数据');
    return;
  }

  // 重置表单数据
  paperVoteForm.ownerId = ''; // 清空业主主键
  paperVoteForm.housingId = String(voteList.value[0].housingId); // 从投票数据中获取 housingId 并转换为字符串

  // 初始化投票列表数据，确保所有选项都是未选中状态
  paperVoteForm.voteList = voteList.value.map((vote) => ({
    voteId: vote.voteId,
    voteTitle: vote.voteTitle,
    advice: '' // 清空选择
  }));

  // 重置表单校验状态
  paperVoteFormRef.value?.resetFields();

  paperVoteDialog.visible = true;
  paperVoteDialog.title = '纸质投票';
};

/** 提交纸质投票 */
const submitPaperVote = async () => {
  if (!paperVoteForm.ownerId) {
    ElMessage.warning('请输入业主主键');
    return;
  }

  // 修改校验规则：检查是否至少选择了一项
  const hasAnyVote = paperVoteForm.voteList.some((vote) => vote.advice);
  if (!hasAnyVote) {
    ElMessage.warning('请至少选择一项投票');
    return;
  }

  try {
    buttonLoading.value = true;
    // 只提交已选中的投票
    const submitForm = {
      ...paperVoteForm,
      voteList: paperVoteForm.voteList.filter(vote => vote.advice)
    };
    await paperVoteInsert(submitForm);
    ElMessage.success('提交成功');
    paperVoteDialog.visible = false;
    await getList();
  } catch (error) {
    console.error('Submit paper vote error:', error);
    ElMessage.error('投票提交失败，请稍后重试');
  } finally {
    buttonLoading.value = false;
  }
};

/** 取消纸质投票 */
const cancelPaperVote = () => {
  paperVoteDialog.visible = false;
  paperVoteForm.ownerId = '';
  paperVoteForm.housingId = undefined;
  paperVoteForm.voteList = [];
};

onMounted(() => {
  getList();
});
</script>

<style scoped>
.paper-vote-container {
  max-height: 60vh;
  overflow-y: auto;
  padding: 15px;
}

.vote-card {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 15px;
  background: #fff;
}

.vote-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #409eff;
}

.vote-options {
  padding: 0 30px;
}

.options-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.vote-option {
  flex: 1;
  text-align: center;
  padding: 0 25px;  /* 增加左右内边距 */
}

:deep(.el-radio) {
  margin-right: 0;
}

:deep(.el-radio__label) {
  font-size: 14px;
}

:deep(.el-radio__inner) {
  border-color: #DCDFE6;
}

:deep(.el-radio__input.is-checked .el-radio__inner) {
  border-color: #409EFF;
  background: #409EFF;
}

/* 确保单选框和文字在同一行 */
:deep(.el-radio__input) {
  vertical-align: middle;
}

.owner-input {
  margin-bottom: 20px;
  padding: 0 20px;
  display: flex;
  align-items: center;
}

.owner-label {
  color: #606266;
  margin-right: 12px;
  min-width: 70px;
}

</style>
