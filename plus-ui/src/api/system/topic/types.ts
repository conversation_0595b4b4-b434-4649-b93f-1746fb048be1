export interface TopicVO {
  /**
   * 议题主键
   */
  topicId: string | number;

  /**
   * 议题名称
   */
  topicName: string;

  /**
   * 议题文件
   */
  file: string;

  /**
   * 小区主键
   */
  housingId: string | number;

  /**
   * 小区名称
   */
  housingName: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 议题开始时间
   */
  topicStartTime: string;

  /**
   * 议题结束时间
   */
  topicEndTime: string;

  /**
   * 议题选项
   */
  topicOptions: string;

  /**
   * 议题选项数组
   */
  topicOptionArray: string[];

  /**
   * 议题投票
   */
  votes: number;
}

export interface TopicForm extends BaseEntity {
  /**
   * 议题主键
   */
  topicId?: string | number;

  /**
   * 议题名称
   */
  topicName?: string;

  /**
   * 议题文件
   */
  file?: string;

  /**
   * 小区主键
   */
  housingId?: string | number;

  /**
   * 议题开始时间
   */
  topicStartTime?: string;

  /**
   * 议题结束时间
   */
  topicEndTime?: string;

  /**
   * 议题选项
   */
  topicOptions?: string;

  /**
   * 议题选项
   */
  topicOptionsArray?: string[];

}

export interface TopicQuery extends PageQuery {

  /**
   * 议题名称
   */
  topicName?: string;

  /**
   * 议题文件
   */
  file?: string;

  /**
   * 小区名称
   */
  housingName?: string;

  /**
   * 议题开始时间
   */
  topicStartTime?: string;

  /**
   * 议题结束时间
   */
  topicEndTime?: string;

  /**
   * 议题选项
   */
  topicOptions?: string;

  /**
   * 日期范围参数
   */
  params?: any;

  /**
   * 小区主键
   */
  housingId?: string | number;
}


