import request
  from '@/utils/request';
import {
  AxiosPromise
} from 'axios';
import {
  ProjectForm,
  ProjectQuery,
  ProjectVO
} from '@/api/system/project/types';

/**
 * 查询项目工程列表
 * @param query
 * @returns {*}
 */

export const listProject = (query?: ProjectQuery): AxiosPromise<ProjectVO[]> => {
  return request({
    url: '/system/project/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询项目工程列表
 * @returns {*}
 */

export const projectSelect = (): AxiosPromise<ProjectVO[]> => {
  return request({
    url: '/system/project/select',
    method: 'get'
  });
};

/**
 * 查询项目工程详细
 * @param projectId
 */
export const getProject = (projectId: string | number): AxiosPromise<ProjectVO> => {
  return request({
    url: '/system/project/' + projectId,
    method: 'get'
  });
};

/**
 * 新增项目工程
 * @param data
 */
export const addProject = (data: ProjectForm) => {
  return request({
    url: '/system/project',
    method: 'post',
    data: data
  });
};

/**
 * 修改项目工程
 * @param data
 */
export const updateProject = (data: ProjectForm) => {
  return request({
    url: '/system/project',
    method: 'put',
    data: data
  });
};

/**
 * 删除项目工程
 * @param projectId
 */
export const delProject = (projectId: string | number | Array<string | number>) => {
  return request({
    url: '/system/project/' + projectId,
    method: 'delete'
  });
};
