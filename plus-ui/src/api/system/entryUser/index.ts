import request
  from '@/utils/request';
import {
  AxiosPromise
} from 'axios';
import {
  EntryUserForm,
  EntryUserQuery,
  EntryUserVO
} from '@/api/system/entryUser/types';

/**
 * 查询报名人员列表
 * @param query
 * @returns {*}
 */

export const listEntryUser = (query?: EntryUserQuery): AxiosPromise<EntryUserVO[]> => {
  return request({
    url: '/system/entryUser/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询报名人员详细
 * @param entryUserId
 */
export const getEntryUser = (entryUserId: string | number): AxiosPromise<EntryUserVO> => {
  return request({
    url: '/system/entryUser/' + entryUserId,
    method: 'get'
  });
};

/**
 * 新增报名人员
 * @param data
 */
export const addEntryUser = (data: EntryUserForm) => {
  return request({
    url: '/system/entryUser',
    method: 'post',
    data: data
  });
};

/**
 * 修改报名人员
 * @param data
 */
export const updateEntryUser = (data: EntryUserForm) => {
  return request({
    url: '/system/entryUser',
    method: 'put',
    data: data
  });
};

/**
 * 删除报名人员
 * @param entryUserId
 */
export const delEntryUser = (entryUserId: string | number | Array<string | number>) => {
  return request({
    url: '/system/entryUser/' + entryUserId,
    method: 'delete'
  });
};
