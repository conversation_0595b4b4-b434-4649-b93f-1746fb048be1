import request
  from '@/utils/request';
import {
  MassTextingForm,
  NoticeExportForm,
  NoticeForm,
  NoticeQuery,
  NoticeVO
} from './types';
import {
  AxiosPromise
} from 'axios';

// 查询公告列表
export function listNotice(query: NoticeQuery): AxiosPromise<NoticeVO[]> {
  return request({
    url: '/system/notice/list',
    method: 'get',
    params: query
  });
}

// 查询公告详细
export function getNotice(noticeId: string | number): AxiosPromise<NoticeVO> {
  return request({
    url: '/system/notice/' + noticeId,
    method: 'get'
  });
}

// 查询公告下拉列表
export function noticeSelect(): AxiosPromise<NoticeVO[]> {
  return request({
    url: '/system/notice/noticeSelect',
    method: 'get'
  });
}

// 新增公告
export function addNotice(data: NoticeForm) {
  return request({
    url: '/system/notice',
    method: 'post',
    data: data
  });
}

// 修改公告
export function updateNotice(data: NoticeForm) {
  return request({
    url: '/system/notice',
    method: 'put',
    data: data
  });
}

// 删除公告
export function delNotice(noticeId: string | number | Array<string | number>) {
  return request({
    url: '/system/notice/' + noticeId,
    method: 'delete'
  });
}

// 导出公告
export function exportNotice(data: NoticeExportForm) {
  return request({
    url: '/system/notice/export',
    method: 'post',
    data: data
  });
}

// 群发业主短信
export function massTexting(data: MassTextingForm) {
  return request({
    url: '/system/notice/massTexting',
    method: 'post',
    data: data
  });
}
