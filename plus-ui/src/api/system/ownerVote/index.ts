import request
  from '@/utils/request';
import {
  AxiosPromise
} from 'axios';
import {
  OwnerVoteForm,
  OwnerVoteQuery,
  OwnerVoteVO
} from '@/api/system/ownerVote/types';

/**
 * 查询业主投票列表
 * @param query
 * @returns {*}
 */

export const listOwnerVote = (query?: OwnerVoteQuery): AxiosPromise<OwnerVoteVO[]> => {
  return request({
    url: '/system/ownerVote/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询业主投票详细
 * @param ownerVoteId
 */
export const getOwnerVote = (ownerVoteId: string | number): AxiosPromise<OwnerVoteVO> => {
  return request({
    url: '/system/ownerVote/' + ownerVoteId,
    method: 'get'
  });
};

/**
 * 新增业主投票
 * @param data
 */
export const addOwnerVote = (data: OwnerVoteForm) => {
  return request({
    url: '/system/ownerVote',
    method: 'post',
    data: data
  });
};

/**
 * 修改业主投票
 * @param data
 */
export const updateOwnerVote = (data: OwnerVoteForm) => {
  return request({
    url: '/system/ownerVote',
    method: 'put',
    data: data
  });
};

/**
 * 删除业主投票
 * @param ownerVoteId
 */
export const delOwnerVote = (ownerVoteId: string | number | Array<string | number>) => {
  return request({
    url: '/system/ownerVote/' + ownerVoteId,
    method: 'delete'
  });
};
